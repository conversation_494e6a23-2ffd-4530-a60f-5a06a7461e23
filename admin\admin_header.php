<?php
// Admin-specific header that bypasses closure checks
// Check if a session is already active before starting a new one
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Determine the base path of the application
$base_path = '/';

// Calculate cart item count (for consistency)
$cart_item_count = 0;
if (isset($_SESSION['cart'])) {
    foreach ($_SESSION['cart'] as $item) {
        $cart_item_count += $item['quantity'];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Panel - ElsBee Data</title>
  <link rel="icon" type="image/png" href="<?php echo $base_path; ?>image/icon-512.png">
  
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <link href="<?php echo $base_path; ?>assets/css/style.css" rel="stylesheet">
  
  <style>
    /* Admin-specific styles */
    .admin-header {
      background: linear-gradient(135deg, #6a6ee7, #5f5be3);
      color: white;
      padding: 1rem 0;
      margin-bottom: 0;
    }
    
    .admin-badge {
      background: rgba(255, 255, 255, 0.2);
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.875rem;
    }
    
    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 0.5rem;
    }
    
    .status-open { background-color: #28a745; }
    .status-closed { background-color: #dc3545; }
    .status-unknown { background-color: #6c757d; }
  </style>
</head>
<body>

<!-- Admin Header -->
<div class="admin-header">
  <div class="container">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h4 class="mb-0">
          <i class="bi bi-shield-check"></i> Admin Panel
          <span class="admin-badge">ElsBee Data</span>
        </h4>
      </div>
      <div class="d-flex align-items-center">
        <?php if (isset($_SESSION['user'])): ?>
          <div class="me-3">
            <small>Welcome, <?php echo htmlspecialchars($_SESSION['user']['full_name']); ?></small>
          </div>
          <div class="dropdown">
            <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="bi bi-person-circle"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="<?php echo $base_path; ?>index.php"><i class="bi bi-house"></i> Main Site</a></li>
              <li><a class="dropdown-item" href="<?php echo $base_path; ?>profile/index.php"><i class="bi bi-person"></i> Profile</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="<?php echo $base_path; ?>logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
            </ul>
          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
</div>

<!-- Admin Navigation -->
<nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
  <div class="container">
    <div class="navbar-nav">
      <a class="nav-link" href="<?php echo $base_path; ?>admin/dashboard.php">
        <i class="bi bi-speedometer2"></i> Dashboard
      </a>
      <a class="nav-link" href="<?php echo $base_path; ?>admin/users.php">
        <i class="bi bi-people"></i> Users
      </a>
      <a class="nav-link" href="<?php echo $base_path; ?>admin/products.php">
        <i class="bi bi-box"></i> Products
      </a>
      <a class="nav-link" href="<?php echo $base_path; ?>admin/orders.php">
        <i class="bi bi-bag"></i> Orders
      </a>
      <a class="nav-link" href="<?php echo $base_path; ?>admin/reviews.php">
        <i class="bi bi-star"></i> Reviews
      </a>
      <a class="nav-link" href="<?php echo $base_path; ?>admin/settings.php">
        <i class="bi bi-gear"></i> Settings
      </a>
    </div>
  </div>
</nav>

<div class="content">

<script>
// Admin-specific JavaScript
function displayAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.alert-container') || document.body;
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    alertContainer.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<!-- Container for dynamic alerts -->
<div class="alert-container position-fixed" style="top: 10px; right: 10px; z-index: 1050; max-width: 350px;"></div>
