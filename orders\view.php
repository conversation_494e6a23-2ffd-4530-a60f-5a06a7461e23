<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Determine the base path of the application (assuming this file is in a subdirectory)
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require_login($base_path);

$user_id = $_SESSION['user']['id'];
$order_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$stmt = $conn->prepare('SELECT * FROM orders WHERE id = ? AND user_id = ?');
$stmt->bind_param('ii', $order_id, $user_id);
$stmt->execute();
$order = $stmt->get_result()->fetch_assoc();

include '../includes/header.php';
if (!$order): ?>
<div class="container py-5">
  <div class="alert alert-danger">Order not found or access denied.</div>
</div>
<?php include '../includes/footer.php'; exit; endif; ?>
<div class="container py-5">
  <h2 class="mb-4">Order #<?php echo $order['id']; ?></h2>
  <div class="mb-3">
    <strong>Date:</strong> <?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?><br>
    <strong>Status:</strong> <span class="badge"><?php echo $order['status']; ?></span><br>
    <strong>Total:</strong> &#8373;<?php echo number_format($order['total'],2); ?>
  </div>
  <h5>Order Items</h5>
  <div class="table-responsive">
    <table class="table align-middle">
      <thead>
        <tr>
          <th>Product</th>
          <th>Data Size</th>
          <th>Quantity</th>
          <th>Price</th>
          <th>Beneficiary Number</th>
        </tr>
      </thead>
      <tbody>
      <?php
      $stmt = $conn->prepare('SELECT oi.*, p.name FROM order_items oi JOIN products p ON oi.product_id = p.id WHERE oi.order_id = ?');
      $stmt->bind_param('i', $order_id);
      $stmt->execute();
      $items = $stmt->get_result();
      while($item = $items->fetch_assoc()): ?>
        <tr>
          <td><?php echo htmlspecialchars($item['name']); ?></td>
          <td><?php echo htmlspecialchars($item['data_size']); ?></td>
          <td><?php echo $item['quantity']; ?></td>
          <td>&#8373;<?php echo htmlspecialchars(number_format($item['price'],2)); ?></td>
          <td><?php echo htmlspecialchars($item['beneficiary_number'] ?? 'N/A'); ?></td>
        </tr>
      <?php endwhile; ?>
      </tbody>
    </table>
  </div>
  <a href="index.php" class="btn btn-secondary mt-3">Back to Orders</a>
</div>
<div class="container py-5">
  <h5>Order Details</h5>
  <div class="table-responsive">
    <table class="table align-middle">
      <tbody>
        <tr>
          <th>Order ID:</th>
          <td>#<?php echo $order['id']; ?></td>
        </tr>
        <tr>
          <th>Reference Code:</th>
          <td>
            <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($order['reference_code']); ?></code>
          </td>
        </tr>
        <tr>
          <th>Status:</th>
          <td><?php echo $order['status']; ?></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
<?php include '../includes/footer.php'; ?> 