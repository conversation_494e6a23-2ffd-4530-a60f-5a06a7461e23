<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();
if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: /index.php');
    exit;
}
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Instead of deleting, we'll set the product and its data sizes to inactive
$conn->begin_transaction();

try {
    // Set product to inactive
    $stmt = $conn->prepare('UPDATE products SET active = 0 WHERE id = ?');
    $stmt->bind_param('i', $id);
    $stmt->execute();

    // Set all associated data sizes to inactive
    $stmt = $conn->prepare('UPDATE data_sizes SET active = 0 WHERE product_id = ?');
    $stmt->bind_param('i', $id);
    $stmt->execute();

    $conn->commit();
    header('Location: products.php?deactivated=1');
} catch (Exception $e) {
    $conn->rollback();
    header('Location: products.php?error=1');
}
exit; 