<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Where's our app?
$base_path = '/datahub/'; // Change this if needed

// Gotta be logged in to delete
require_login($base_path);

$redirect_url = $base_path . 'reviews/index.php';

// Someone trying to delete?
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['review_id'])) {
    $review_id = filter_input(INPUT_POST, 'review_id', FILTER_VALIDATE_INT);
    $user_id = $_SESSION['user']['id'];

    if ($review_id) {
        // Make sure they wrote the review
        $check_stmt = $conn->prepare('SELECT id FROM reviews WHERE id = ? AND user_id = ?');
        $check_stmt->bind_param('ii', $review_id, $user_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();

        if ($result->num_rows === 1) {
            // Ok, delete it
            $delete_stmt = $conn->prepare('DELETE FROM reviews WHERE id = ?');
            $delete_stmt->bind_param('i', $review_id);

            if ($delete_stmt->execute()) {
                // Done!
                header('Location: ' . $redirect_url . '?delete_success=1');
                exit;
            } else {
                // Oops
                error_log('Review deletion failed: ' . $conn->error);
                header('Location: ' . $redirect_url . '?delete_error=' . urlencode('Failed to delete review.'));
                exit;
            }
        }
        
        // Can't find it or not their review
        header('Location: ' . $redirect_url . '?delete_error=' . urlencode('Review not found or you do not have permission to delete it.'));
        exit;
    } else {
        // Bad review ID
        header('Location: ' . $redirect_url . '?delete_error=' . urlencode('Invalid review ID.'));
        exit;
    }
} else {
    // Wrong way to delete
    header('Location: ' . $redirect_url . '?delete_error=' . urlencode('Invalid request.'));
    exit;
}
?> 