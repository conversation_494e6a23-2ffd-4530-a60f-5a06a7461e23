<?php
session_start();
require 'includes/db.php';
require 'includes/auth.php';

// Determine the base path of the application
$base_path = '/'; // Adjust this if your application is in a different subdirectory

// Require user to be logged in to access this page
require_login($base_path);

$user_id = $_SESSION['user']['id'];
$error = '';
$success = '';
$order_details = null;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $full_name = trim($_POST['full_name']);
    $gh_card_number = trim($_POST['gh_card_number']);
    $region = trim($_POST['region']);
    $beneficiary_number = trim($_POST['beneficiary_number']);
    $id_type = 'GH Card'; // Fixed value
    $bundle_price = 10.00; // Fixed price for AFA bundle
    $product_name = 'AFA Bundle'; // Assuming a product name
    $product_id = 0; // You'll need to get the actual product ID for the AFA bundle
                     // For now, we'll use 0 or you can fetch it from the database

    // Basic validation
    if (empty($full_name) || empty($gh_card_number) || empty($region) || empty($beneficiary_number)) {
        $error = 'Please fill in all required fields.';
    } else {
        // Find the product ID for 'AFA Bundle'
        $stmt = $conn->prepare('SELECT id FROM products WHERE name = ?');
        $stmt->bind_param('s', $product_name);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($product = $result->fetch_assoc()) {
            $product_id = $product['id'];
        } else {
            // Handle case where AFA Bundle product is not found in the database
            $error = 'AFA Bundle product not found in our system.';
        }

        if (!$error) {
            // Generate a unique 4-digit reference code (basic implementation)
            // NOTE: This is a simple approach. For a production system, you'd want a more robust method to ensure uniqueness.
            $reference_code_numeric = str_pad(rand(0, 9999), 4, '0', STR_PAD_LEFT);
            $reference_code = $reference_code_numeric . '_MTNAFA';

            // Insert order into the database
            $conn->begin_transaction(); // Start transaction
            try {
                $stmt = $conn->prepare('INSERT INTO orders (user_id, total, status, payment_status, reference_code) VALUES (?, ?, ?, ?, ?)');
                $initial_status = 'Pending';
                $initial_payment_status = 'Awaiting Manual Payment';
                $stmt->bind_param('idsss', $user_id, $bundle_price, $initial_status, $initial_payment_status, $reference_code);
                $stmt->execute();
                $order_id = $conn->insert_id;

                // Insert order item into the database
                $stmt = $conn->prepare('INSERT INTO order_items (order_id, product_id, data_size, quantity, price, full_name, gh_card_number, id_type, region, beneficiary_number) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
                $data_size = 'N/A'; // Or whatever is appropriate for AFA
                $quantity = 1;
                $stmt->bind_param('iididsssss', $order_id, $product_id, $data_size, $quantity, $bundle_price, $full_name, $gh_card_number, $id_type, $region, $beneficiary_number);
                $stmt->execute();

                $conn->commit(); // Commit transaction
                $success = 'Order placed successfully!';
                // Store order details to display payment instructions
                $order_details = ['id' => $order_id, 'total' => $bundle_price, 'reference_code' => $reference_code];

            } catch (mysqli_sql_exception $e) {
                $conn->rollback(); // Rollback transaction on error
                $error = 'Error placing order: ' . $e->getMessage();
                 error_log('Order placement failed: ' . $e->getMessage());
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="container py-5">
    <h2 class="mb-4">AFA Bundle Order</h2>

    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>

    <?php if ($success && $order_details): ?>
        <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title">Manual Payment Instructions</h4>
                <p>Your order has been placed. Please follow the instructions below to complete your payment.</p>
                <p><strong>Order ID:</strong> #<?php echo $order_details['id']; ?></p>
                <p><strong>Amount Due:</strong> GHS <?php echo number_format($order_details['total'], 2); ?></p>
                <p><strong>Your Reference Code:</strong> <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($order_details['reference_code']); ?></code></p>
                <p>Send the exact amount to the following Mobile Money details:</p>
                <p><strong>Mobile Money Number:</strong> **********</p>
                <p><strong>Account Name:</strong> Joseph Atta Brown</p>
                <p><strong>IMPORTANT:</strong> Include your **Reference Code (<?php echo htmlspecialchars($order_details['reference_code']); ?>)** in the payment description/reference field.</p>
                <p>Your order will be processed once your payment is manually confirmed by the administrator.</p>
            </div>
        </div>
         <p><a href="index.php" class="btn btn-primary">Back to Home</a></p>
    <?php else: ?>
        <p>Please fill out the form below to order your AFA Bundle.</p>
        <form method="post">
            <div class="mb-3">
                <label for="full_name" class="form-label">Full Name</label>
                <input type="text" class="form-control" id="full_name" name="full_name" required>
            </div>
            <div class="mb-3">
                <label for="gh_card_number" class="form-label">Gh Card Number</label>
                <input type="text" class="form-control" id="gh_card_number" name="gh_card_number" required>
            </div>
            <div class="mb-3">
                <label for="id_type" class="form-label">ID Type</label>
                <input type="text" class="form-control" id="id_type" name="id_type" value="GH Card" readonly>
            </div>
            <div class="mb-3">
                <label for="region" class="form-label">Region</label>
                <input type="text" class="form-control" id="region" name="region" required>
            </div>
            <div class="mb-3">
                <label for="beneficiary_number" class="form-label">MTN Number</label>
                <input type="tel" class="form-control" id="beneficiary_number" name="beneficiary_number" pattern="[0-9]{10}" placeholder="Enter 10-digit MTN number" required>
                <div class="form-text">Enter the 10-digit MTN number that will receive the bundle.</div>
            </div>
            <input type="hidden" name="price" value="10.00"> <!-- Hidden input for price -->
            <button type="submit" class="btn btn-primary">Pay Now (GHS 10.00)</button>
        </form>
    <?php endif; ?>

</div>

<?php include 'includes/footer.php'; ?> 