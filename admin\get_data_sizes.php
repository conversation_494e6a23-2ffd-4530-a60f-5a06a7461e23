<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();

if ($_SESSION['user']['account_type'] !== 'Admin') {
    // Consider a more appropriate response for AJAX, e.g., JSON error
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

$product_id = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;

if ($product_id <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid product ID']);
    exit;
}

// Fetch data sizes for the given product ID
$data_sizes_stmt = $conn->prepare('SELECT id, size, price, active FROM data_sizes WHERE product_id = ? ORDER BY price ASC');
$data_sizes_stmt->bind_param('i', $product_id);
$data_sizes_stmt->execute();
$data_sizes_result = $data_sizes_stmt->get_result();

$data_sizes = [];
while($ds = $data_sizes_result->fetch_assoc()) {
    $data_sizes[] = $ds;
}

$data_sizes_stmt->close();

echo json_encode($data_sizes);
?> 