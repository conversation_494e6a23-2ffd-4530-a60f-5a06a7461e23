<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();

if ($_SESSION['user']['account_type'] !== 'Admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$data_size_id = isset($_POST['data_size_id']) ? intval($_POST['data_size_id']) : 0;
$size = isset($_POST['size']) ? trim($_POST['size']) : '';
$price = isset($_POST['price']) ? floatval($_POST['price']) : 0.00;
$active = isset($_POST['active']) ? 1 : 0;

if ($data_size_id <= 0 || empty($size) || $price <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid input data']);
    exit;
}

// Check if data size exists
$check_stmt = $conn->prepare('SELECT id FROM data_sizes WHERE id = ?');
$check_stmt->bind_param('i', $data_size_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();
if ($check_result->num_rows === 0) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Data size not found']);
    exit;
}
$check_stmt->close();

$stmt = $conn->prepare('UPDATE data_sizes SET size = ?, price = ?, active = ? WHERE id = ?');
$stmt->bind_param('sidi', $size, $price, $active, $data_size_id);

if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'Data plan updated successfully!']);
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $stmt->error]);
}

$stmt->close();
$conn->close();
?> 