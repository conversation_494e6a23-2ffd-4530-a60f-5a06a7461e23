<?php
// Start session and include necessary files
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Determine the base path of the application
$base_path = '/';

require 'includes/db.php';
require 'includes/time_manager.php';

// If website is not closed, redirect to main page
if (!is_website_closed()) {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

// Get time information
$next_opening = get_next_opening_time();
$time_info = get_time_until_status_change();

// Calculate cart item count for display
$cart_item_count = 0;
if (isset($_SESSION['cart'])) {
    foreach ($_SESSION['cart'] as $item) {
        $cart_item_count += $item['quantity'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>We're Closed - ElsBee Data</title>
  <link rel="icon" type="image/png" href="<?php echo $base_path; ?>image/icon-512.png">
  
  <!-- Meta Tags -->
  <meta name="title" content="We're Closed - ElsBee Data">
  <meta name="description" content="ElsBee Data is currently closed. We'll be back at 7 AM Ghana time. Thank you for your patience.">
  
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <link href="<?php echo $base_path; ?>assets/css/style.css" rel="stylesheet">
  
  <style>
    body {
      background: linear-gradient(135deg,
        #6a6ee7 0%,
        #5f5be3 25%,
        #4b4fcf 50%,
        #5f5be3 75%,
        #6a6ee7 100%
      );
      background-size: 400% 400%;
      animation: gradientBG 15s ease infinite;
      height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
      position: relative;
      overflow: hidden;
      margin: 0;
    }

    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at center, 
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0) 70%
      );
      pointer-events: none;
    }

    @keyframes gradientBG {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    .closed-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border: none;
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
                  0 0 0 1px rgba(255, 255, 255, 0.1);
      padding: 2rem !important;
      max-width: 600px;
      width: 100%;
      position: relative;
      z-index: 1;
      text-align: center;
      margin: auto;
      max-height: 90vh;
      overflow-y: auto;
    }

    .closed-icon {
      font-size: 3rem;
      color: var(--primary);
      margin-bottom: 1rem;
      animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }

    .closed-title {
      color: var(--primary-dark);
      font-size: 2.2rem;
      font-weight: 700;
      margin-bottom: 0.8rem;
    }

    .closed-message {
      color: #495057;
      font-size: 1.1rem;
      line-height: 1.5;
      margin-bottom: 1.5rem;
    }

    .time-info {
      background: rgba(106, 110, 231, 0.1);
      border-radius: 15px;
      padding: 1.2rem;
      margin-bottom: 1.5rem;
    }

    .countdown {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--primary-dark);
      margin-bottom: 0.4rem;
    }

    .next-opening {
      color: #6c757d;
      font-size: 0.95rem;
    }

    .home-btn {
      background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
      color: #fff;
      font-weight: 600;
      padding: 0.7rem 1.8rem;
      border-radius: 12px;
      font-size: 1rem;
      border: none;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
    }

    .home-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(106, 110, 231, 0.3);
      color: #fff;
    }

    .schedule-item {
      padding: 0.5rem;
      border-radius: 8px;
      background: rgba(106, 110, 231, 0.05);
      transition: all 0.3s ease;
    }

    .schedule-item:hover {
      background: rgba(106, 110, 231, 0.1);
      transform: translateY(-2px);
    }

    .schedule-icon {
      font-size: 1.5rem;
      color: var(--primary);
      margin-bottom: 0.3rem;
      display: block;
    }

    .schedule-label {
      font-weight: 600;
      color: #495057;
      font-size: 0.9rem;
      margin-bottom: 0.2rem;
    }

    .schedule-time {
      color: #6c757d;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .floating-elements {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 0;
    }

    .floating-elements::before,
    .floating-elements::after {
      content: '';
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;
    }

    .floating-elements::before {
      width: 100px;
      height: 100px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .floating-elements::after {
      width: 150px;
      height: 150px;
      bottom: 20%;
      right: 10%;
      animation-delay: 3s;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
      body {
        padding: 0.5rem;
      }

      .closed-card {
        padding: 2rem 1.5rem !important;
        margin: 0;
        border-radius: 15px;
        max-width: 100%;
      }

      .closed-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }

      .closed-title {
        font-size: 2rem;
        margin-bottom: 0.8rem;
      }

      .closed-message {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
      }

      .time-info {
        padding: 1rem;
        margin-bottom: 1.5rem;
      }

      .countdown {
        font-size: 1.2rem;
      }

      .home-btn {
        padding: 0.7rem 1.5rem;
        font-size: 1rem;
      }
    }

    /* Small Mobile Devices */
    @media (max-width: 480px) {
      .closed-card {
        padding: 1.5rem 1rem !important;
        border-radius: 12px;
      }

      .closed-icon {
        font-size: 2.5rem;
      }

      .closed-title {
        font-size: 1.8rem;
      }

      .closed-message {
        font-size: 1rem;
        line-height: 1.5;
      }

      .countdown {
        font-size: 1.1rem;
      }

      .next-opening {
        font-size: 0.9rem;
      }

      .time-info {
        padding: 0.8rem;
      }

      .home-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.95rem;
      }

      .schedule-item {
        padding: 0.4rem 0.2rem;
        margin-bottom: 0.5rem;
      }

      .schedule-icon {
        font-size: 1.2rem;
        margin-bottom: 0.2rem;
      }

      .schedule-label {
        font-size: 0.8rem;
        margin-bottom: 0.1rem;
      }

      .schedule-time {
        font-size: 0.7rem;
      }
    }

    /* Large Desktop Centering */
    @media (min-width: 1200px) {
      .closed-card {
        max-width: 650px;
        padding: 2.5rem !important;
      }

      .closed-title {
        font-size: 2.4rem;
      }

      .closed-message {
        font-size: 1.15rem;
      }

      .countdown {
        font-size: 1.4rem;
      }
    }

    /* Extra Large Screens */
    @media (min-width: 1400px) {
      .closed-card {
        max-width: 700px;
        padding: 3rem !important;
      }

      .closed-title {
        font-size: 2.6rem;
      }

      .closed-message {
        font-size: 1.2rem;
      }

      .countdown {
        font-size: 1.5rem;
      }
    }

    /* Ensure content fits in viewport */
    @media (min-height: 600px) and (min-width: 768px) {
      .closed-card {
        max-height: 85vh;
      }
    }

    @media (max-height: 700px) and (min-width: 768px) {
      .closed-card {
        padding: 1.5rem !important;
        max-height: 90vh;
      }

      .closed-icon {
        font-size: 2.5rem;
        margin-bottom: 0.8rem;
      }

      .closed-title {
        font-size: 2rem;
        margin-bottom: 0.6rem;
      }

      .closed-message {
        font-size: 1rem;
        margin-bottom: 1.2rem;
      }

      .time-info {
        padding: 1rem;
        margin-bottom: 1.2rem;
      }

      .countdown {
        font-size: 1.2rem;
      }
    }
  </style>
</head>
<body>
  <div class="floating-elements"></div>
  
  <div class="container-fluid d-flex align-items-center justify-content-center">
    <div class="row w-100 justify-content-center">
      <div class="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5">
        <div class="card closed-card">
          <div class="closed-icon">
            <i class="bi bi-moon-stars"></i>
          </div>
          
          <h1 class="closed-title">We're Closed</h1>
          
          <p class="closed-message">
            Sorry, we have closed for today... please come back tomorrow at 7am. Thank You
          </p>

          <div class="mb-3">
            <div class="row text-center g-2">
              <div class="col-4">
                <div class="schedule-item">
                  <i class="bi bi-clock-history schedule-icon"></i>
                  <div class="schedule-label">Closed</div>
                  <div class="schedule-time">9:00 PM</div>
                </div>
              </div>
              <div class="col-4">
                <div class="schedule-item">
                  <i class="bi bi-moon schedule-icon"></i>
                  <div class="schedule-label">Sleeping</div>
                  <div class="schedule-time">10 Hours</div>
                </div>
              </div>
              <div class="col-4">
                <div class="schedule-item">
                  <i class="bi bi-sunrise schedule-icon"></i>
                  <div class="schedule-label">Opens</div>
                  <div class="schedule-time">7:00 AM</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="time-info">
            <div class="countdown" id="countdown">
              Loading...
            </div>
            <div class="next-opening">
              We'll be back at <strong><?php echo $next_opening->format('g:i A'); ?></strong>
            </div>
          </div>
          
          <div class="mb-3">
            <small class="text-muted">
              <i class="bi bi-clock"></i> 
              Current Ghana Time: <span id="current-time"><?php echo get_ghana_time_display(); ?></span>
            </small>
          </div>
          
          <a href="<?php echo $base_path; ?>index.php" class="home-btn">
            <i class="bi bi-house"></i> Check if We're Open
          </a>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <script>
    // Time management and auto-redirect functionality
    const GHANA_TIMEZONE_OFFSET = <?php echo get_ghana_timezone_offset(); ?>; // Ghana timezone offset in minutes
    let countdownInterval;
    
    function getGhanaTime() {
      const now = new Date();
      const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
      return new Date(utc + (GHANA_TIMEZONE_OFFSET * 60000));
    }
    
    function updateCurrentTime() {
      const ghanaTime = getGhanaTime();
      const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      };
      document.getElementById('current-time').textContent = ghanaTime.toLocaleDateString('en-US', options);
    }
    
    function isWebsiteOpen() {
      const ghanaTime = getGhanaTime();
      const hour = ghanaTime.getHours();
      return hour >= 7 && hour < 21; // Open from 7 AM to 9 PM
    }
    
    function getNextOpeningTime() {
      const ghanaTime = getGhanaTime();
      const hour = ghanaTime.getHours();
      
      let nextOpening = new Date(ghanaTime);
      
      if (hour < 7) {
        // Opening is today at 7 AM
        nextOpening.setHours(7, 0, 0, 0);
      } else {
        // Opening is tomorrow at 7 AM
        nextOpening.setDate(nextOpening.getDate() + 1);
        nextOpening.setHours(7, 0, 0, 0);
      }
      
      return nextOpening;
    }
    
    function updateCountdown() {
      const now = getGhanaTime();
      const nextOpening = getNextOpeningTime();
      const diff = nextOpening.getTime() - now.getTime();
      
      if (diff <= 0) {
        // Time to open! Redirect to main page
        window.location.href = '<?php echo $base_path; ?>index.php';
        return;
      }
      
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      
      document.getElementById('countdown').textContent = 
        `Opening in ${hours}h ${minutes}m ${seconds}s`;
    }
    
    function checkWebsiteStatus() {
      if (isWebsiteOpen()) {
        // Website should be open, redirect to main page
        window.location.href = '<?php echo $base_path; ?>index.php';
      }
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      updateCurrentTime();
      updateCountdown();
      
      // Update current time every minute
      setInterval(updateCurrentTime, 60000);
      
      // Update countdown every second
      countdownInterval = setInterval(updateCountdown, 1000);
      
      // Check website status every minute
      setInterval(checkWebsiteStatus, 60000);
    });
  </script>
</body>
</html>
