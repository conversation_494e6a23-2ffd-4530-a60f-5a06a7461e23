<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();
if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: /index.php');
    exit;
}
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($id == $_SESSION['user']['id']) {
    include '../includes/header.php';
    echo '<div class="container py-5"><div class="alert alert-danger">You cannot delete your own account.</div><a href="users.php" class="btn btn-secondary mt-3">Back</a></div>';
    include '../includes/footer.php';
    exit;
}
$stmt = $conn->prepare('DELETE FROM users WHERE id = ?');
$stmt->bind_param('i', $id);
$stmt->execute();
header('Location: users.php?deleted=1');
exit; 