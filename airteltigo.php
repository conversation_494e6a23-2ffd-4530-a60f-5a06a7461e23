<?php
session_start();
require 'includes/db.php';
include 'includes/header.php';

$network = 'Airtel';
$stmt = $conn->prepare('
    SELECT p.*,
           MIN(ds.price) as min_price,
           MAX(ds.price) as max_price
    FROM products p
    LEFT JOIN data_sizes ds ON p.id = ds.product_id AND ds.active = 1
    WHERE p.network = ? AND p.active = 1
    GROUP BY p.id
    LIMIT 1
');
$stmt->bind_param('s', $network);
$stmt->execute();
$product_result = $stmt->get_result();
$product = $product_result->fetch_assoc();

if (!$product || ($product['min_price'] === null && $product['max_price'] === null)): ?>
<div class="container py-5">
  <div class="alert alert-danger">
    <i class="bi bi-exclamation-circle me-2"></i>No AirtelTigo products found or available.
  </div>
  <div class="text-center mt-3">
    <a href="index.php" class="btn btn-primary">Back to Home</a>
  </div>
</div>
<?php include 'includes/footer.php'; exit; endif; ?>

<?php
// Fetch data sizes for this product
$data_sizes_stmt = $conn->prepare('SELECT id, size, price FROM data_sizes WHERE product_id = ? AND active = 1 ORDER BY price ASC');
$data_sizes_stmt->bind_param('i', $product['id']);
$data_sizes_stmt->execute();
$data_sizes_result = $data_sizes_stmt->get_result();

?>

<div class="container py-5">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="<?php echo '/'; ?>index.php">Home</a></li>
      <li class="breadcrumb-item"><a href="#"><?php echo $network; ?></a></li>
      <li class="breadcrumb-item active" aria-current="page"><?php echo $network; ?> Data Bundles</li>
    </ol>
  </nav>
  <div class="row">
    <div class="col-md-5 text-center mb-4 mb-md-0">
      <img src="https://citinewsroom.com/wp-content/uploads/2022/04/Fluid-logo.png" class="img-fluid" alt="<?php echo $network; ?> Logo">
    </div>
    <div class="col-md-7">
      <h2><?php echo htmlspecialchars($product['name']); ?></h2>
      <?php if($product['min_price'] !== null): ?>
        <h4 class="text-success">&#8373;<?php echo number_format($product['min_price'],2); ?> - &#8373;<?php echo number_format($product['max_price'],2); ?></h4>
      <?php else: ?>
        <h4 class="text-warning">Prices not available</h4>
      <?php endif; ?>
      <p class="text-success">+ Free Shipping</p>
      <p class="text-danger fw-bold">MAKE SURE NUMBER RECEIVING DATA DOESN'T OWE AIRTIME ELSE DATA WON'T BE RECEIVED AND MONEY NON REFUNDABLE !!!</p>
      <p>All data packages are non expiry.</p>
      <?php if(isset($_SESSION['user'])): ?>
        <form id="airteltigo-product-form" method="post" action="<?php echo '/'; ?>cart/add.php">
          <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
          <div class="mb-3">
            <label class="form-label">DATA SIZE</label>
            <select name="data_size_id" class="form-select" required>
              <option value="">Choose an option</option>
              <?php
              while ($size_option = $data_sizes_result->fetch_assoc()):
              ?>
                <option value="<?php echo htmlspecialchars($size_option['id']); ?>"><?php echo htmlspecialchars($size_option['size']) . ' - GHS' . htmlspecialchars(number_format($size_option['price'], 2)); ?></option>
              <?php endwhile; ?>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">Quantity</label>
            <input type="number" name="quantity" class="form-control" value="1" min="1" max="10" required>
          </div>
          <div class="mb-3">
            <label for="beneficiary_number" class="form-label">Beneficiary Number</label>
            <input type="text" class="form-control" id="beneficiary_number" name="beneficiary_number" placeholder="e.g., 02XXXXXXXX" required>
          </div>
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-cart-plus me-2"></i>ADD TO CART
          </button>
        </form>
      <?php else: ?>
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i>Please <a href="login.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>" class="alert-link">login</a> to purchase this product.
        </div>
      <?php endif; ?>
      
    </div>
  </div>
  <div class="row mt-5">
    <div class="col-md-8">
      <ul class="nav nav-tabs" id="descTab" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="desc-tab" data-bs-toggle="tab" data-bs-target="#desc" type="button" role="tab">Description</button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab">Additional information</button>
        </li>
      </ul>
      <div class="tab-content p-3 border border-top-0" id="descTabContent">
        <div class="tab-pane fade show active" id="desc" role="tabpanel">
          <?php echo nl2br(htmlspecialchars($product['description'])); ?>
        </div>
        <div class="tab-pane fade" id="info" role="tabpanel">
          <!-- Additional info can go here -->
          <div class="mt-4">
           <!-- Network specific details can go here -->
           <ul>
             <li><b>Important notice for AirtelTigo users:</b></li>
             <li>Ensure your number is active and not blocked.</li>
             <li>Confirm network coverage in your area before purchase.</li>
           </ul>
           <div class="mt-3">
             <b>No refund for the following errors at your end:</b>
             <ul>
               <li>Numbers less than 10 digit</li>
               <li>Wrong recipient/number</li>
               <li>Placing data for wrong network.</li>
             </ul>
           </div>
         </div>
          
      </div>
    </div>
        
  </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add to cart form handler (existing)
    const cartForm = document.querySelector('#airteltigo-product-form');
    if (cartForm) {
        cartForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(cartForm);

            fetch('<?php echo '/'; ?>cart/add.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAlert(data.message, 'success');
                    const cartCountElement = document.querySelector('.cart-count');
                    if (cartCountElement) {
                        cartCountElement.innerText = data.cart_count;
                    }
                } else {
                    displayAlert('Error adding item to cart: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                displayAlert('An unexpected error occurred. Please try again.', 'danger');
            });
        });
    }

    // Review form handler (new)
    const reviewForm = document.querySelector('#airteltigo-review-form');
    if (reviewForm) {
        const starIcons = reviewForm.querySelectorAll('.star-rating i');
        const ratingInput = document.getElementById('airteltigo-rating');
        let selectedRating = 0;

        // Add click listeners to star icons
        starIcons.forEach(star => {
            star.addEventListener('click', function() {
                selectedRating = parseInt(this.dataset.rating);
                ratingInput.value = selectedRating; // Set the hidden input value
                // Update star appearance
                starIcons.forEach((s, index) => {
                    if (index < selectedRating) {
                        s.classList.replace('bi-star', 'bi-star-fill');
                        s.classList.add('text-warning');
                    } else {
                        s.classList.replace('bi-star-fill', 'bi-star');
                        s.classList.remove('text-warning');
                    }
                });
            });
        });

        reviewForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Basic validation
            if (selectedRating === 0) {
                displayAlert('Please select a star rating.', 'warning');
                return;
            }
            const reviewTextarea = document.getElementById('airteltigo-review-text');
            if (reviewTextarea.value.trim() === '') {
                 displayAlert('Please enter your review text.', 'warning');
                 return;
            }

            const formData = new FormData(reviewForm);
            // FormData automatically includes the product_id, rating, and review_text inputs

            fetch('<?php echo '/'; ?>submit_review.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAlert(data.message, 'success');
                    // Clear the form on success
                    reviewForm.reset();
                    selectedRating = 0; // Reset selected rating
                    starIcons.forEach(star => { // Reset star appearance
                        star.classList.replace('bi-star-fill', 'bi-star');
                        star.classList.remove('text-warning');
                    });
                    // Optionally, you might want to refresh the displayed reviews here
                    // This would require another AJAX call to fetch reviews and update the #reviews tab content
                    // For now, a page refresh or manual check might be needed to see the new review
                     setTimeout(() => {
                         window.location.reload(); // Simple page refresh to show new review
                     }, 2000); // Refresh after 2 seconds

                } else {
                    displayAlert('Error submitting review: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                displayAlert('An unexpected error occurred while submitting your review. Please try again.', 'danger');
            });
        });
    }
});

</script> 