<?php
session_start();
require '../includes/db.php'; // Include database connection

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $product_id = intval($_POST['product_id']);
    $data_size_id = intval($_POST['data_size_id']); // Get data_size_id instead of data_size string
    $quantity = intval($_POST['quantity']);
    $beneficiary_number = trim($_POST['beneficiary_number']); // Get beneficiary number

    // Fetch data size details from the database
    $stmt = $conn->prepare('SELECT id, size, price FROM data_sizes WHERE id = ? AND active = 1');
    $stmt->bind_param('i', $data_size_id);
    $stmt->execute();
    $data_size_result = $stmt->get_result();
    $data_size_details = $data_size_result->fetch_assoc();

    // Fetch product details (specifically the name)
    $stmt = $conn->prepare('SELECT name FROM products WHERE id = ?');
    $stmt->bind_param('i', $product_id);
    $stmt->execute();
    $product_result = $stmt->get_result();
    $product_details = $product_result->fetch_assoc();

    // Check if data size and product exist and data size is active
    if ($data_size_details && $product_details) {
        if (!isset($_SESSION['cart'])) $_SESSION['cart'] = [];
        
        // Use a unique key based on product_id and data_size_id
        $key = $product_id . '_' . $data_size_id;
        
        if (isset($_SESSION['cart'][$key])) {
            $_SESSION['cart'][$key]['quantity'] += $quantity;
        } else {
            $_SESSION['cart'][$key] = [
                'product_id' => $product_id,
                'name' => $product_details['name'], // Add product name
                'data_size_id' => $data_size_id, // Store data_size_id
                'size' => $data_size_details['size'], // Store data size string
                'price' => $data_size_details['price'], // Store price
                'quantity' => $quantity,
                'beneficiary_number' => $beneficiary_number // Store beneficiary number
            ];
        }
        // Instead of redirecting, return a JSON success response
        echo json_encode(['success' => true, 'message' => 'Item added to cart successfully!']);
        exit;
    } else {
        // Handle case where data size or product is not found or data size is not active
        // Return a JSON error response
        http_response_code(400); // Bad Request
        echo json_encode(['success' => false, 'error' => 'Invalid product or data size selected.']);
        exit;
    }
}

// If not a POST request, return a method not allowed JSON response
http_response_code(405); // Method Not Allowed
echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
exit; 