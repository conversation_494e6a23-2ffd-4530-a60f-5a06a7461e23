<?php
require '../includes/db.php';
require '../includes/mtn_payment.php';

// MTN API credentials - Replace these with your actual credentials
$mtn_client_id = 'YOUR_CLIENT_ID';
$mtn_client_secret = 'YOUR_CLIENT_SECRET';
$mtn_subscription_key = 'YOUR_SUBSCRIPTION_KEY';
$mtn_environment = 'sandbox'; // Change to 'production' for live environment

// Get the X-Reference-Id from the headers
$transaction_id = $_SERVER['HTTP_X_REFERENCE_ID'] ?? '';

if (empty($transaction_id)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing transaction ID']);
    exit;
}

$mtn = new MTNPayment($mtn_client_id, $mtn_client_secret, $mtn_subscription_key, $mtn_environment);

// Get transaction status
$status_result = $mtn->getTransactionStatus($transaction_id);

if ($status_result['success']) {
    // Get order ID from transaction
    $stmt = $conn->prepare('SELECT order_id FROM payment_transactions WHERE transaction_id = ?');
    $stmt->bind_param('s', $transaction_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $transaction = $result->fetch_assoc();
    
    if ($transaction) {
        $order_id = $transaction['order_id'];
        
        // Update transaction status
        $stmt = $conn->prepare('UPDATE payment_transactions SET status = ?, updated_at = NOW() WHERE transaction_id = ?');
        $stmt->bind_param('ss', $status_result['status'], $transaction_id);
        $stmt->execute();
        
        // Update order status based on payment status
        if ($status_result['status'] === 'SUCCESSFUL') {
            $stmt = $conn->prepare('UPDATE orders SET payment_status = ?, status = ? WHERE id = ?');
            $payment_status = 'PAID';
            $order_status = 'PROCESSING';
            $stmt->bind_param('ssi', $payment_status, $order_status, $order_id);
            $stmt->execute();
            
            // Here you can add code to trigger data bundle delivery
            // For example, call your data delivery API or update inventory
        } elseif ($status_result['status'] === 'FAILED') {
            $stmt = $conn->prepare('UPDATE orders SET payment_status = ? WHERE id = ?');
            $payment_status = 'FAILED';
            $stmt->bind_param('si', $payment_status, $order_id);
            $stmt->execute();
        }
    }
}

// Always return 200 OK to MTN
http_response_code(200);
echo json_encode(['success' => true]);
?> 