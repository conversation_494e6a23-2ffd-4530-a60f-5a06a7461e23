<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Where's our app?
$base_path = '/datahub/'; // Change this if needed

// Gotta be logged in to edit
require_login($base_path);

$redirect_url = $base_path . 'reviews/index.php';

// Someone trying to edit?
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['review_id'], $_POST['rating'], $_POST['review_text'])) {
    $review_id = filter_input(INPUT_POST, 'review_id', FILTER_VALIDATE_INT);
    $rating = filter_input(INPUT_POST, 'rating', FILTER_VALIDATE_INT);
    $review_text = filter_input(INPUT_POST, 'review_text', FILTER_SANITIZE_STRING);
    $user_id = $_SESSION['user']['id'];

    // Check the data
    if (!$review_id || $rating === false || $rating < 1 || $rating > 5 || empty($review_text)) {
        // Bad data
        header('Location: ' . $redirect_url . '?edit_error=' . urlencode('Invalid input for review edit.'));
        exit;
    }

    // Make sure they wrote the review
    $check_stmt = $conn->prepare('SELECT id FROM reviews WHERE id = ? AND user_id = ?');
    $check_stmt->bind_param('ii', $review_id, $user_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();

    if ($result->num_rows === 1) {
        // Ok, update it
        $update_stmt = $conn->prepare('UPDATE reviews SET rating = ?, review = ? WHERE id = ?');
        $update_stmt->bind_param('isi', $rating, $review_text, $review_id);

        if ($update_stmt->execute()) {
            // Done!
            header('Location: ' . $redirect_url . '?edit_success=1');
            exit;
        } else {
            // Oops
            error_log('Review update failed: ' . $conn->error);
            header('Location: ' . $redirect_url . '?edit_error=' . urlencode('Failed to update review.'));
            exit;
        }
    } else {
        // Can't find it or not their review
        header('Location: ' . $redirect_url . '?edit_error=' . urlencode('Review not found or you do not have permission to edit it.'));
        exit;
    }
} else {
    // Wrong way to edit
    header('Location: ' . $redirect_url . '?edit_error=' . urlencode('Invalid request for review edit.'));
    exit;
}
?> 