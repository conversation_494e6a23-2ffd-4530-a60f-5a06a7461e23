<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Determine the base path of the application (assuming this file is in a subdirectory)
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require_login($base_path);

$user_id = $_SESSION['user']['id'];
$orders = $conn->query("SELECT * FROM orders WHERE user_id = $user_id ORDER BY created_at DESC");
include '../includes/header.php';
?>
<div class="container py-5">
  <h2 class="mb-4 text-center">My Orders</h2>
  <?php if(isset($_GET['error'])): ?><div class="alert alert-danger">Error fetching orders. Please try again later.</div><?php endif; ?>
  <?php if($orders->num_rows == 0): ?>
    <div class="text-center">
      <div class="mb-3"><i class="bi bi-bag fs-1 text-muted"></i></div>
      <h5>No Orders Yet</h5>
      <p>You haven't placed any orders yet. Start shopping to see your orders here.</p>
      <a href="../store/index.php" class="btn btn-primary">Start Shopping</a>
    </div>
  <?php else: ?>
    <div class="table-responsive">
      <table class="table align-middle">
        <thead>
          <tr>
            <th>Order ID</th>
            <th>Date</th>
            <th>Status</th>
            <th>Payment Status</th>
            <th>Total (GHS)</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
        <?php while($order = $orders->fetch_assoc()): ?>
          <tr>
            <td>#<?php echo $order['id']; ?></td>
            <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
            <td>
              <?php
                $status = $order['status'];
                $badge_class = '';
                switch ($status) {
                  case 'Pending':
                    $badge_class = 'bg-warning';
                    break;
                  case 'Completed':
                    $badge_class = 'bg-success';
                    break;
                  case 'Cancelled':
                    $badge_class = 'bg-danger';
                    break;
                  default:
                    $badge_class = 'bg-secondary'; // Default color for unknown status
                }
              ?>
              <span class="badge <?php echo $badge_class; ?>"><?php echo htmlspecialchars($status); ?></span>
            </td>
            <td>
               <?php
                $payment_status = $order['payment_status'];
                $badge_class = '';
                switch ($payment_status) {
                  case 'Awaiting Manual Payment':
                    $badge_class = 'bg-warning';
                    break;
                  case 'PAID':
                    $badge_class = 'bg-success';
                    break;
                  case 'FAILED':
                    $badge_class = 'bg-danger';
                    break;
                  default:
                    $badge_class = 'bg-secondary';
                }
              ?>
              <span class="badge <?php echo $badge_class; ?>"><?php echo htmlspecialchars($payment_status); ?></span>
            </td>
            <td><?php echo number_format($order['total'],2); ?></td>
            <td><a href="view.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-outline-primary">View</a></td>
          </tr>
        <?php endwhile; ?>
        </tbody>
      </table>
    </div>
  <?php endif; ?>
</div>
<?php include '../includes/footer.php'; ?> 