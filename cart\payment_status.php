<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require '../includes/mtn_payment.php';

// Determine the base path of the application
$base_path = '/datahub/';

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    header('Location: ' . $base_path . 'login.php');
    exit;
}

$order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
$transaction_id = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';

if (!$order_id || !$transaction_id) {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

// MTN API credentials - Replace these with your actual credentials
$mtn_client_id = 'YOUR_CLIENT_ID';
$mtn_client_secret = 'YOUR_CLIENT_SECRET';
$mtn_subscription_key = 'YOUR_SUBSCRIPTION_KEY';
$mtn_environment = 'sandbox'; // Change to 'production' for live environment

$mtn = new MTNPayment($mtn_client_id, $mtn_client_secret, $mtn_subscription_key, $mtn_environment);

// Check transaction status
$status_result = $mtn->getTransactionStatus($transaction_id);

// Update transaction status in database
if ($status_result['success']) {
    $stmt = $conn->prepare('UPDATE payment_transactions SET status = ?, updated_at = NOW() WHERE transaction_id = ?');
    $stmt->bind_param('ss', $status_result['status'], $transaction_id);
    $stmt->execute();
    
    // If payment is successful, update order status
    if ($status_result['status'] === 'SUCCESSFUL') {
        $stmt = $conn->prepare('UPDATE orders SET payment_status = ?, status = ? WHERE id = ?');
        $payment_status = 'PAID';
        $order_status = 'PROCESSING';
        $stmt->bind_param('ssi', $payment_status, $order_status, $order_id);
        $stmt->execute();
        
        // Redirect to success page
        header('Location: ' . $base_path . 'cart/success.php?order_id=' . $order_id);
        exit;
    } elseif ($status_result['status'] === 'FAILED') {
        $stmt = $conn->prepare('UPDATE orders SET payment_status = ? WHERE id = ?');
        $payment_status = 'FAILED';
        $stmt->bind_param('si', $payment_status, $order_id);
        $stmt->execute();
    }
}

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body text-center">
                    <h3 class="card-title mb-4">Payment Status</h3>
                    
                    <?php if ($status_result['success']): ?>
                        <?php if ($status_result['status'] === 'PENDING'): ?>
                            <div class="alert alert-warning">
                                <i class="bi bi-hourglass-split fs-1"></i>
                                <h4 class="mt-3">Payment Pending</h4>
                                <p>Please complete the payment on your phone.</p>
                                <p>You will be redirected automatically when the payment is completed.</p>
                            </div>
                            <script>
                                // Check payment status every 5 seconds
                                setTimeout(function() {
                                    window.location.reload();
                                }, 5000);
                            </script>
                        <?php elseif ($status_result['status'] === 'FAILED'): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-x-circle fs-1"></i>
                                <h4 class="mt-3">Payment Failed</h4>
                                <p>The payment was not successful. Please try again.</p>
                                <a href="<?php echo $base_path; ?>cart/checkout.php?order_id=<?php echo $order_id; ?>" class="btn btn-primary mt-3">Try Again</a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle fs-1"></i>
                                <h4 class="mt-3">Payment Status: <?php echo htmlspecialchars($status_result['status']); ?></h4>
                                <p>Please wait while we process your payment...</p>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-circle fs-1"></i>
                            <h4 class="mt-3">Error Checking Payment Status</h4>
                            <p><?php echo htmlspecialchars($status_result['error'] ?? 'An error occurred while checking payment status'); ?></p>
                            <a href="<?php echo $base_path; ?>cart/checkout.php?order_id=<?php echo $order_id; ?>" class="btn btn-primary mt-3">Back to Checkout</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?> 