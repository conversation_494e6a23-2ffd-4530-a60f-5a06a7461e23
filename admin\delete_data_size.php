<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();

if ($_SESSION['user']['account_type'] !== 'Admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Expecting data_size_id via POST
$data_size_id = isset($_POST['data_size_id']) ? intval($_POST['data_size_id']) : 0;

if ($data_size_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid data size ID']);
    exit;
}

// Check if data size exists and get its product_id before deleting
$check_stmt = $conn->prepare('SELECT product_id FROM data_sizes WHERE id = ?');
$check_stmt->bind_param('i', $data_size_id);
$check_stmt->execute();
$result = $check_stmt->get_result();
$data_size = $result->fetch_assoc();

if (!$data_size) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Data size not found']);
    exit;
}

$product_id = $data_size['product_id'];
$check_stmt->close();


$stmt = $conn->prepare('DELETE FROM data_sizes WHERE id = ?');
$stmt->bind_param('i', $data_size_id);

if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'Data plan deleted successfully!', 'product_id' => $product_id]);
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $stmt->error]);
}

$stmt->close();
$conn->close();
?> 