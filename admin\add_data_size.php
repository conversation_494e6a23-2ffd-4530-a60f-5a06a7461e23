<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();

if ($_SESSION['user']['account_type'] !== 'Admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
$size = isset($_POST['size']) ? trim($_POST['size']) : '';
$price = isset($_POST['price']) ? floatval($_POST['price']) : 0.00;
$active = isset($_POST['active']) ? 1 : 0;

if ($product_id <= 0 || empty($size) || $price <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid input data']);
    exit;
}

// Check if product exists
$check_stmt = $conn->prepare('SELECT id FROM products WHERE id = ?');
$check_stmt->bind_param('i', $product_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();
if ($check_result->num_rows === 0) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Product not found']);
    exit;
}
$check_stmt->close();

$stmt = $conn->prepare('INSERT INTO data_sizes (product_id, size, price, active) VALUES (?, ?, ?, ?)');
$stmt->bind_param('isdi', $product_id, $size, $price, $active);

if ($stmt->execute()) {
    echo json_encode(['success' => true, 'message' => 'Data plan added successfully!']);
} else {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $stmt->error]);
}

$stmt->close();
$conn->close();
?> 