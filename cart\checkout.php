<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Determine the base path of the application
$base_path = '/';

require_login($base_path);

$cart = $_SESSION['cart'] ?? [];
$order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : null;

// Redirect to cart if cart is empty AND no order_id is present in the URL
if (empty($cart) && !$order_id) {
    header('Location: index.php');
    exit;
}

$error = '';
$order_details = null;

if ($order_id) {
    // Fetch order details
    $stmt = $conn->prepare('SELECT * FROM orders WHERE id = ? AND user_id = ?');
    $stmt->bind_param('ii', $order_id, $_SESSION['user']['id']);
    $stmt->execute();
    $order_details = $stmt->get_result()->fetch_assoc();
    
    // No payment button needed for manual payment
}

// Add this function at the top of the file after the requires
function generateUniqueReferenceCode($conn) {
    do {
        // Generate a random 4-digit number
        $code = str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
        
        // Check if this code already exists
        $stmt = $conn->prepare('SELECT id FROM orders WHERE reference_code = ?');
        $stmt->bind_param('s', $code);
        $stmt->execute();
        $result = $stmt->get_result();
    } while ($result->num_rows > 0); // Keep generating until we find a unique code
    
    return $code;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = $_SESSION['user']['id'];
    $total = 0;
    
    // Calculate total from cart items
    foreach ($cart as $item) {
        $price = $item['price'] ?? 0;
        $total += $price * $item['quantity'];
    }

    // Start transaction
    $conn->begin_transaction();

    try {
        // Get the beneficiary number from the first item in the cart
        $user_number = '';
        $first_product_network = ''; // Initialize variable for network
        if (!empty($cart)) {
            $first_item = reset($cart);
            $user_number = $first_item['beneficiary_number'] ?? '';

            // Fetch network of the first product
            if (isset($first_item['product_id'])) {
                $stmt_network = $conn->prepare('SELECT network FROM products WHERE id = ?');
                $stmt_network->bind_param('i', $first_item['product_id']);
                $stmt_network->execute();
                $result_network = $stmt_network->get_result();
                if ($result_network->num_rows > 0) {
                    $network_row = $result_network->fetch_assoc();
                    $first_product_network = $network_row['network'];
                }
                $stmt_network->close();
            }
        }

        // Generate unique reference code
        $reference_code = generateUniqueReferenceCode($conn);
        
        // Append network suffix if available
        if (!empty($first_product_network)) {
            $reference_code .= '_' . strtoupper(str_replace(' ', '', $first_product_network));
        }

        // Insert into orders table with reference code
        $stmt = $conn->prepare('INSERT INTO orders (user_id, user_number, total, status, payment_status, reference_code) VALUES (?, ?, ?, "PENDING", "Awaiting Manual Payment", ?)');
        $stmt->bind_param('isds', $user_id, $user_number, $total, $reference_code);
        $stmt->execute();
        $order_id = $conn->insert_id;

        // Insert into order_items table
        $stmt = $conn->prepare('INSERT INTO order_items (order_id, product_id, data_size, quantity, price, beneficiary_number) VALUES (?, ?, ?, ?, ?, ?)');
        foreach ($cart as $item) {
            $data_size = $item['size'] ?? 'N/A';
            $price = $item['price'] ?? 0;
            $beneficiary_number = $item['beneficiary_number'] ?? '';
            $stmt->bind_param('iissds', $order_id, $item['product_id'], $data_size, $item['quantity'], $price, $beneficiary_number);
            $stmt->execute();
        }

        $conn->commit();
        unset($_SESSION['cart']);

        // Redirect to the same page with order_id to show manual payment instructions
        header('Location: checkout.php?order_id=' . $order_id);
        exit;

    } catch (Exception $e) {
        $conn->rollback();
        $error = 'Error placing order: ' . $e->getMessage();
        error_log('Order placement failed: ' . $e->getMessage());
    }
}

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <?php if ($order_details): ?>
                <div class="card mb-4">
                    <div class="card-body">
                        <h3 class="card-title">Order #<?php echo $order_id; ?></h3>
                        <p class="text-muted">Total: GHS <?php echo number_format($order_details['total'], 2); ?></p>
                        
                        <?php if ($order_details['payment_status'] === 'Awaiting Manual Payment'): ?>
                            <div class="alert alert-info">
                                <h4 class="alert-heading"><i class="bi bi-cash me-2"></i>Manual Payment Instructions</h4>
                                <p>Please send the total amount of <strong>GHS <?php echo number_format($order_details['total'], 2); ?></strong> via Mobile Money to the number below:</p>
                                
                                <p class="lead text-center"><strong>0599672113</strong></p>
                                <p class="text-center mb-4">Recipient Name: <strong>Joseph Atta Brown</strong></p>

                                <div class="alert alert-warning">
                                    <p class="mb-2"><strong>IMPORTANT:</strong> Use this unique reference code for your payment:</p>
                                    <div class="d-flex align-items-center justify-content-center gap-2">
                                        <code class="fs-4 bg-light px-3 py-2 rounded" id="referenceCode"><?php echo htmlspecialchars($order_details['reference_code']); ?></code>
                                        <button class="btn btn-sm btn-outline-primary" onclick="copyReferenceCode()">
                                            <i class="bi bi-clipboard"></i> Copy
                                        </button>
                                    </div>
                                </div>
                                
                                <p>Your order will be processed once your payment is confirmed.</p>
                            </div>

                            <script>
                            function copyReferenceCode() {
                                const code = document.getElementById('referenceCode').textContent;
                                navigator.clipboard.writeText(code).then(() => {
                                    const btn = event.target.closest('button');
                                    const originalHtml = btn.innerHTML;
                                    btn.innerHTML = '<i class="bi bi-check"></i> Copied!';
                                    setTimeout(() => {
                                        btn.innerHTML = originalHtml;
                                    }, 2000);
                                });
                            }
                            </script>
                        <?php elseif ($order_details['payment_status'] === 'PAID'): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>Payment confirmed! Your order is being processed.
                            </div>
                            <a href="<?php echo '/'; ?>orders/view.php?id=<?php echo $order_id; ?>" class="btn btn-primary">View Order</a>
                        <?php elseif ($order_details['payment_status'] === 'FAILED'): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-x-circle me-2"></i>Payment failed or could not be confirmed. Please contact support or try placing a new order.
                            </div>
                             <a href="<?php echo '/'; ?>index.php" class="btn btn-primary mt-3">Continue Shopping</a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card">
                    <div class="card-body">
                        <h4 class="card-title">Order Items</h4>
                        <?php
                        $stmt = $conn->prepare('
                            SELECT oi.*, p.name as product_name 
                            FROM order_items oi 
                            JOIN products p ON oi.product_id = p.id 
                            WHERE oi.order_id = ?
                        ');
                        $stmt->bind_param('i', $order_id);
                        $stmt->execute();
                        $items = $stmt->get_result();
                        ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Size</th>
                                        <th>Quantity</th>
                                        <th>Price</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($item = $items->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                            <td><?php echo htmlspecialchars($item['data_size']); ?></td>
                                            <td><?php echo $item['quantity']; ?></td>
                                            <td>GHS <?php echo number_format($item['price'], 2); ?></td>
                                            <td>GHS <?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title">Checkout</h3>
                        <?php if (empty($cart)): ?>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>Your cart is empty.
                            </div>
                            <a href="<?php echo '/'; ?>index.php" class="btn btn-primary">Continue Shopping</a>
                        <?php else: ?>
                            <form method="post">
                                <div class="mb-3">
                                    <h4>Order Summary</h4>
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Size</th>
                                                    <th>Quantity</th>
                                                    <th>Price</th>
                                                    <th>Total</th>
                                                    <th>Beneficiary Number</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php 
                                                $total = 0;
                                                foreach ($cart as $item): 
                                                    $item_total = ($item['price'] ?? 0) * $item['quantity'];
                                                    $total += $item_total;
                                                ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                                                        <td><?php echo htmlspecialchars($item['size']); ?></td>
                                                        <td><?php echo $item['quantity']; ?></td>
                                                        <td>GHS <?php echo number_format($item['price'], 2); ?></td>
                                                        <td>GHS <?php echo number_format($item_total, 2); ?></td>
                                                        <td><?php echo htmlspecialchars($item['beneficiary_number'] ?? ''); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="4" class="text-end">Total:</th>
                                                    <th>GHS <?php echo number_format($total, 2); ?></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-credit-card me-2"></i>Place Order and Get Payment Instructions
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?> 
