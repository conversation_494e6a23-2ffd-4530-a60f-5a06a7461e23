<?php
session_start();

// Determine the base path of the application (assuming this file is in a subdirectory)
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require '../includes/db.php';
require '../includes/auth.php';
require '../includes/time_manager.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$users = $conn->query('SELECT COUNT(*) as c FROM users')->fetch_assoc()['c'];
$orders = $conn->query('SELECT COUNT(*) as c FROM orders')->fetch_assoc()['c'];
$products = $conn->query('SELECT COUNT(*) as c FROM products')->fetch_assoc()['c'];
$reviews = $conn->query('SELECT COUNT(*) as c FROM reviews')->fetch_assoc()['c'];

// Get website closure status
$status_info = get_closure_status_info();
$admin_override = get_admin_closure_override();

include '../includes/header.php';
?>
<div class="container py-5">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">Admin Dashboard</h2>
    <div class="website-status">
      <span class="badge <?php echo $status_info['is_closed'] ? 'bg-danger' : 'bg-success'; ?> fs-6">
        <i class="bi bi-<?php echo $status_info['is_closed'] ? 'x-circle' : 'check-circle'; ?>"></i>
        Website <?php echo $status_info['is_closed'] ? 'CLOSED' : 'OPEN'; ?>
        <?php if ($admin_override !== null): ?>
          <small>(Manual Override)</small>
        <?php endif; ?>
      </span>
    </div>
  </div>
  <div class="row g-4 mb-4">
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $users; ?></h4>
        <div class="text-muted">Users</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $orders; ?></h4>
        <div class="text-muted">Orders</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $products; ?></h4>
        <div class="text-muted">Products</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $reviews; ?></h4>
        <div class="text-muted">Reviews</div>
      </div>
    </div>
  </div>
  <div class="row g-3">
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/users.php" class="btn btn-primary w-100 shadow-sm">Manage Users</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/products.php" class="btn btn-primary w-100 shadow-sm">Manage Products</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/orders.php" class="btn btn-primary w-100 shadow-sm">Manage Orders</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/reviews.php" class="btn btn-primary w-100 shadow-sm">Manage Reviews</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/website_closure.php" class="btn btn-warning w-100 shadow-sm"><i class="bi bi-clock"></i> Website Closure</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/settings.php" class="btn btn-secondary w-100 shadow-sm">System Settings</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/activity_logs.php" class="btn btn-secondary w-100 shadow-sm">Activity Logs</a></div>
  </div>
</div>
<?php include '../includes/footer.php'; ?> 