<?php
session_start();

// Determine the base path of the application (assuming this file is in a subdirectory)
$base_path = '/'; // Adjust this if your application is in a different subdirectory

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$users = $conn->query('SELECT COUNT(*) as c FROM users')->fetch_assoc()['c'];
$orders = $conn->query('SELECT COUNT(*) as c FROM orders')->fetch_assoc()['c'];
$products = $conn->query('SELECT COUNT(*) as c FROM products')->fetch_assoc()['c'];
$reviews = $conn->query('SELECT COUNT(*) as c FROM reviews')->fetch_assoc()['c'];

// Check if closure system is set up
$closure_system_ready = false;
$status_info = ['is_closed' => false, 'reason' => 'system_not_ready', 'message' => 'Closure system not set up'];
$admin_override = null;

try {
    // Check if settings table exists
    $result = $conn->query("SHOW TABLES LIKE 'settings'");
    if ($result->num_rows > 0) {
        require '../includes/time_manager.php';
        $status_info = get_closure_status_info();
        $admin_override = get_admin_closure_override();
        $closure_system_ready = true;
    }
} catch (Exception $e) {
    // If time manager fails, continue without it
    error_log("Time manager error in admin dashboard: " . $e->getMessage());
}

include '../includes/header.php';
?>
<div class="container py-5">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">Admin Dashboard</h2>
    <div class="website-status">
      <?php if ($closure_system_ready): ?>
        <span class="badge <?php echo $status_info['is_closed'] ? 'bg-danger' : 'bg-success'; ?> fs-6">
          <i class="bi bi-<?php echo $status_info['is_closed'] ? 'x-circle' : 'check-circle'; ?>"></i>
          Website <?php echo $status_info['is_closed'] ? 'CLOSED' : 'OPEN'; ?>
          <?php if ($admin_override !== null): ?>
            <small>(Manual Override)</small>
          <?php endif; ?>
        </span>
      <?php else: ?>
        <span class="badge bg-warning fs-6">
          <i class="bi bi-exclamation-triangle"></i>
          Closure System Not Set Up
        </span>
      <?php endif; ?>
    </div>
  </div>

  <?php if (!$closure_system_ready): ?>
    <div class="alert alert-warning mb-4">
      <h5><i class="bi bi-exclamation-triangle"></i> Setup Required</h5>
      <p class="mb-2">The website closure system needs to be set up before you can use the closure management features.</p>
      <a href="<?php echo $base_path; ?>admin/setup_closure_system.php" class="btn btn-warning">
        <i class="bi bi-gear"></i> Set Up Closure System
      </a>
    </div>
  <?php endif; ?>
  <div class="row g-4 mb-4">
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $users; ?></h4>
        <div class="text-muted">Users</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $orders; ?></h4>
        <div class="text-muted">Orders</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $products; ?></h4>
        <div class="text-muted">Products</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $reviews; ?></h4>
        <div class="text-muted">Reviews</div>
      </div>
    </div>
  </div>
  <div class="row g-3">
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/users.php" class="btn btn-primary w-100 shadow-sm">Manage Users</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/products.php" class="btn btn-primary w-100 shadow-sm">Manage Products</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/orders.php" class="btn btn-primary w-100 shadow-sm">Manage Orders</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/reviews.php" class="btn btn-primary w-100 shadow-sm">Manage Reviews</a></div>
    <?php if ($closure_system_ready): ?>
      <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/website_closure.php" class="btn btn-warning w-100 shadow-sm"><i class="bi bi-clock"></i> Website Closure</a></div>
    <?php else: ?>
      <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/setup_closure_system.php" class="btn btn-outline-warning w-100 shadow-sm"><i class="bi bi-gear"></i> Setup Closure System</a></div>
    <?php endif; ?>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/settings.php" class="btn btn-secondary w-100 shadow-sm">System Settings</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/test_email.php" class="btn btn-info w-100 shadow-sm"><i class="bi bi-envelope-check"></i> Test Email</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>test_time.php" class="btn btn-info w-100 shadow-sm">Test Time System</a></div>
  </div>
</div>
<?php include '../includes/footer.php'; ?> 