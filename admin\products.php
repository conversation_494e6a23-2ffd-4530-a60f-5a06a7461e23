<?php
session_start();

// Determine the base path of the application (assuming this file is in a subdirectory)
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

// Get products (initial query, we'll fetch sizes separately)
$products = $conn->query('SELECT * FROM products ORDER BY id DESC');

include '../includes/header.php';
?>
<div class="container py-5">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">Manage Products</h2>
    <a href="<?php echo $base_path; ?>admin/add_product.php" class="btn btn-success">
      <i class="bi bi-plus-circle me-2"></i>Add New Product
    </a>
  </div>

  <?php if(isset($_GET['deactivated'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      <i class="bi bi-check-circle me-2"></i>Product has been deactivated successfully.
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  <?php endif; ?>
  <?php if(isset($_GET['deleted'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      <i class="bi bi-check-circle me-2"></i>Product has been permanently deleted.
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  <?php endif; ?>
  <?php if(isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      <?php
      $error_message = '';
      switch($_GET['error']) {
          case 'not_found':
              $error_message = 'Product not found.';
              break;
          case 'active_product':
              $error_message = 'Cannot delete an active product. Please deactivate it first.';
              break;
          case 'has_orders':
              $error_message = 'Cannot delete a product that has associated orders.';
              break;
          case 'delete_failed':
              $error_message = 'Failed to delete the product. Please try again.';
              break;
          default:
              $error_message = 'An error occurred.';
      }
      ?>
      <i class="bi bi-exclamation-circle me-2"></i><?php echo $error_message; ?>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  <?php endif; ?>

  <div class="card shadow-sm">
    <div class="card-body p-0">
  <div class="table-responsive">
        <table class="table table-hover align-middle mb-0">
          <thead class="bg-light">
        <tr>
              <th class="border-0" style="width: 60px">#</th>
              <th class="border-0">Product Details</th>
              <th class="border-0">Network</th>
              <th class="border-0">Data Plans</th>
              <th class="border-0 text-center" style="width: 100px">Status</th>
              <th class="border-0 text-end" style="width: 200px">Actions</th>
        </tr>
      </thead>
      <tbody>
      <?php while($p = $products->fetch_assoc()): ?>
            <tr class="product-row" data-bs-toggle="collapse" data-bs-target="#collapse-<?php echo $p['id']; ?>" aria-expanded="false" aria-controls="collapse-<?php echo $p['id']; ?>" style="cursor: pointer;">
              <td class="text-muted"><?php echo $p['id']; ?></td>
              <td>
                <div class="d-flex flex-column">
                  <span class="fw-bold"><?php echo htmlspecialchars($p['name']); ?></span>
                  <?php if(!empty($p['description'])): ?>
                    <small class="text-muted"><?php echo htmlspecialchars(substr($p['description'], 0, 100)) . (strlen($p['description']) > 100 ? '...' : ''); ?></small>
                  <?php endif; ?>
                </div>
              </td>
              <td>
                <span class="badge bg-primary"><?php echo htmlspecialchars($p['network']); ?></span>
              </td>
          <td>
            <?php
                // Fetch only the count of data sizes for the overview row
                $data_sizes_count_stmt = $conn->prepare('SELECT COUNT(*) as count FROM data_sizes WHERE product_id = ?');
                $data_sizes_count_stmt->bind_param('i', $p['id']);
                $data_sizes_count_stmt->execute();
                $data_sizes_count_result = $data_sizes_count_stmt->get_result()->fetch_assoc();
                echo $data_sizes_count_result['count'] . ' Data Plan(s)';
                $data_sizes_count_stmt->close();
            ?>
          </td>
              <td class="text-center">
                <?php if($p['active']): ?>
                  <span class="badge bg-success">Active</span>
                <?php else: ?>
                  <span class="badge bg-danger">Inactive</span>
                <?php endif; ?>
              </td>
              <td class="text-end">
                <div class="btn-group">
                  <a href="edit_product.php?id=<?php echo $p['id']; ?>" class="btn btn-sm btn-outline-primary" title="Edit Product">
                    <i class="bi bi-pencil"></i>
                  </a>
                  <?php if($p['active']): ?>
                    <a href="delete_product.php?id=<?php echo $p['id']; ?>" 
                       class="btn btn-sm btn-outline-danger" 
                       onclick="return confirm('Deactivate this product and all its data sizes? This will hide it from customers but preserve order history.');"
                       title="Deactivate Product">
                      <i class="bi bi-toggle-off"></i>
                    </a>
                  <?php else: ?>
                    <a href="reactivate_product.php?id=<?php echo $p['id']; ?>" 
                       class="btn btn-sm btn-outline-success"
                       title="Reactivate Product">
                      <i class="bi bi-toggle-on"></i>
                    </a>
                     <a href="permanent_delete_product.php?id=<?php echo $p['id']; ?>" 
                       class="btn btn-sm btn-outline-danger" 
                       onclick="return confirm('WARNING: This will permanently delete the product and all its data sizes. This action cannot be undone. Are you sure you want to proceed?');"
                       title="Delete Product Permanently">
                      <i class="bi bi-trash"></i>
                    </a>
                  <?php endif; ?>
                </div>
              </td>
            </tr>
            <tr class="collapse" id="collapse-<?php echo $p['id']; ?>">
              <td colspan="6" class="p-3">
                <div class="data-sizes-content">
                  <!-- Data sizes table will be loaded here via AJAX -->
                  Loading data plans...
                </div>
          </td>
        </tr>
      <?php endwhile; ?>
      </tbody>
    </table>
  </div>
    </div>
  </div>

  <div class="mt-4">
    <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary">
      <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
    </a>
  </div>
</div>

<style>
.table > :not(caption) > * > * {
  padding: 1rem;
}
.badge {
  font-weight: 500;
}
.btn-group .btn {
  padding: 0.375rem 0.75rem;
}
.btn-group .btn i {
  font-size: 1rem;
}
</style>

<!-- Add Data Size Modal -->
<div class="modal fade" id="addDataSizeModal" tabindex="-1" aria-labelledby="addDataSizeModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addDataSizeModalLabel">Add New Data Plan</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="addDataSizeForm">
        <div class="modal-body">
          <input type="hidden" name="product_id" id="add_data_size_product_id">
          <div class="mb-3">
            <label for="size" class="form-label">Data Size</label>
            <input type="text" class="form-control" id="size" name="size" required>
          </div>
          <div class="mb-3">
            <label for="price" class="form-label">Price (GHS)</label>
            <input type="number" step="0.01" class="form-control" id="price" name="price" required>
          </div>
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" value="1" id="active" name="active" checked>
            <label class="form-check-label" for="active">Active</label>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Save Data Plan</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Edit Data Size Modal -->
<div class="modal fade" id="editDataSizeModal" tabindex="-1" aria-labelledby="editDataSizeModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editDataSizeModalLabel">Edit Data Plan</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="editDataSizeForm">
        <div class="modal-body">
          <input type="hidden" name="data_size_id" id="edit_data_size_id">
          <input type="hidden" name="product_id" id="edit_data_size_product_id">
          <div class="mb-3">
            <label for="edit_size" class="form-label">Data Size</label>
            <input type="text" class="form-control" id="edit_size" name="size" required>
          </div>
          <div class="mb-3">
            <label for="edit_price" class="form-label">Price (GHS)</label>
            <input type="number" step="0.01" class="form-control" id="edit_price" name="price" required>
          </div>
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" value="1" id="edit_active" name="active">
            <label class="form-check-label" for="edit_active">Active</label>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
    var productRows = document.querySelectorAll('.product-row');

    productRows.forEach(function (row) {
        row.addEventListener('show.bs.collapse', function () {
            var productId = this.dataset.bsTarget.replace('#collapse-', '');
            var collapseContent = this.closest('tbody').querySelector('#collapse-' + productId + ' .data-sizes-content');

            // Clear previous content and show loading message
            collapseContent.innerHTML = 'Loading data plans...';

            // Fetch data sizes via AJAX
            fetch('<?php echo $base_path; ?>admin/get_data_sizes.php?product_id=' + productId)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok ' + response.statusText);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        collapseContent.innerHTML = '<div class="alert alert-danger">Error: ' + data.error + '</div>';
                    } else if (data.length > 0) {
                        // Build the data sizes table HTML
                        let tableHtml = `
                            <table class="table table-bordered table-sm mb-0">
                                <thead>
                                    <tr>
                                        <th>Size</th>
                                        <th>Price (GHS)</th>
                                        <th class="text-center">Status</th>
                                        <th class="text-end" style="width: 120px">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.forEach(size => {
                            const statusClass = size.active == 1 ? 'success' : 'danger';
                            const statusText = size.active == 1 ? 'Active' : 'Inactive';
                            tableHtml += `
                                <tr data-size-id="${size.id}">
                                    <td>${htmlspecialchars(size.size)}</td>
                                    <td>${htmlspecialchars(parseFloat(size.price).toFixed(2))}</td>
                                    <td class="text-center"><span class="badge bg-${statusClass}">${statusText}</span></td>
                                    <td class="text-end">
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary edit-size-btn" data-id="${size.id}" title="Edit Data Plan"><i class="bi bi-pencil"></i></button>
                                            <button type="button" class="btn btn-outline-danger delete-size-btn" data-id="${size.id}" title="Delete Data Plan" onclick="return confirm('Are you sure you want to delete this data plan?');"><i class="bi bi-trash"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            `;
                        });

                        tableHtml += `
                                </tbody>
                            </table>
                            <button type="button" class="btn btn-sm btn-success mt-3 add-size-btn" data-product-id="${productId}">
                              <i class="bi bi-plus-circle me-2"></i>Add New Data Plan
                            </button>
                        `;

                        collapseContent.innerHTML = tableHtml;

                    } else {
                        collapseContent.innerHTML = '<div class="alert alert-info">No data plans added yet.</div>';
                         let addButtonHtml = `
                            <button type="button" class="btn btn-sm btn-success mt-3 add-size-btn" data-product-id="${productId}">
                              <i class="bi bi-plus-circle me-2"></i>Add New Data Plan
                            </button>
                         `;
                         collapseContent.innerHTML += addButtonHtml;
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    collapseContent.innerHTML = '<div class="alert alert-danger">Failed to load data plans.</div>';
                });
        });
    });

    // Handle click on 'Add New Data Plan' button (delegated)
    document.querySelector('#dataSizes').addEventListener('click', function(e) {
        if (e.target.classList.contains('add-size-btn') || e.target.closest('.add-size-btn')) {
            const button = e.target.closest('.add-size-btn');
            const productId = button.dataset.productId;
            
            // Set the product_id in the modal form
            document.getElementById('add_data_size_product_id').value = productId;

            // Reset form fields and check the active checkbox by default
            const addForm = document.getElementById('addDataSizeForm');
            addForm.reset();
            document.getElementById('active').checked = true;

            // Show the modal
            var addModal = new bootstrap.Modal(document.getElementById('addDataSizeModal'));
            addModal.show();
        }

        // Handle click on 'Edit Data Plan' button (delegated)
        if (e.target.classList.contains('edit-size-btn') || e.target.closest('.edit-size-btn')) {
            const button = e.target.closest('.edit-size-btn');
            const dataSizeId = button.dataset.id;
            const editModalElement = document.getElementById('editDataSizeModal');
            const editModal = new bootstrap.Modal(editModalElement);
            const editForm = document.getElementById('editDataSizeForm');

            // Clear previous data and show loading indicator if needed
            // editForm.reset(); // Maybe not reset until data is loaded

            // Fetch data size details via AJAX
            fetch('<?php echo $base_path; ?>admin/get_data_size.php?data_size_id=' + dataSizeId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const dataSize = data.data;
                        document.getElementById('edit_data_size_id').value = dataSize.id;
                        document.getElementById('edit_data_size_product_id').value = dataSize.product_id;
                        document.getElementById('edit_size').value = dataSize.size;
                        document.getElementById('edit_price').value = parseFloat(dataSize.price).toFixed(2);
                        document.getElementById('edit_active').checked = dataSize.active == 1;

                        // Show the modal after data is loaded
                        editModal.show();
                    } else {
                        // Handle error fetching data size details
                        console.error('Error fetching data size:', data.error);
                        alert('Error loading data plan details: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    alert('Failed to load data plan details.');
                });
        }

        // Handle click on 'Delete Data Plan' button (delegated)
        if (e.target.classList.contains('delete-size-btn') || e.target.closest('.delete-size-btn')) {
            const button = e.target.closest('.delete-size-btn');
            const dataSizeId = button.dataset.id;
            const confirmed = confirm('Are you sure you want to delete this data plan?');
            
            if (confirmed) {
                // Implement AJAX deletion
                const formData = new FormData();
                formData.append('data_size_id', dataSizeId);

                fetch('<?php echo $base_path; ?>admin/delete_data_size.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Data plan deleted successfully!');
                        // Refresh the data sizes table for the product
                        refreshProductDataSizes(data.product_id);
                    } else {
                        alert('Error deleting data plan: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    alert('Failed to delete data plan.');
                });
            }
        }
    });

    // Function to refresh data sizes for a specific product
    function refreshProductDataSizes(productId) {
        // Find the corresponding collapse element and trigger the show event
        const collapseElement = document.getElementById('collapse-' + productId);
        if (collapseElement) {
             // Manually trigger the show event, which will refetch and display the data
            var collapse = new bootstrap.Collapse(collapseElement);
            collapse.show();
        }
    }

    // Handle Add Data Size Form Submission
    document.getElementById('addDataSizeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const form = this;
        const formData = new FormData(form);
        const productId = document.getElementById('add_data_size_product_id').value;

        fetch('<?php echo $base_path; ?>admin/add_data_size.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Data plan added successfully!');
                // Hide modal and refresh data sizes table
                var addModal = bootstrap.Modal.getInstance(document.getElementById('addDataSizeModal'));
                addModal.hide();
                refreshProductDataSizes(productId);
            } else {
                alert('Error adding data plan: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Add Data Size error:', error);
            alert('Failed to add data plan.');
        });
    });

     // Handle Edit Data Size Form Submission
    document.getElementById('editDataSizeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const form = this;
        const formData = new FormData(form);
        const dataSizeId = document.getElementById('edit_data_size_id').value;

         fetch('<?php echo $base_path; ?>admin/edit_data_size.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Data plan updated successfully!');
                // Hide modal and refresh data sizes table
                var editModal = bootstrap.Modal.getInstance(document.getElementById('editDataSizeModal'));
                editModal.hide();
                // To refresh the correct product's data sizes, we need the product_id.
                // Since we don't have it readily available here, we might need to refetch the single data size
                // or pass product_id in the form (which is a good idea). Let's add it to the edit modal form.

                // For now, we'll rely on the delete endpoint returning product_id for refresh, or consider a page reload
                // or refetching just the row. A simpler approach for now is to add product_id to the edit form.
                // Let's add a hidden product_id input to the edit modal.
                // *** Correction: The edit modal form already exists, I need to add the input to it.***
                // I'll add a hidden input for product_id to the edit modal form in the previous edit step.
                // Let's assume product_id is available in the edit form for now.

                // Find the product ID associated with this data size (requires fetching data size details first or adding product_id to form)
                // For now, let's refetch the data size details to get the product_id for refreshing.
                fetch('<?php echo $base_path; ?>admin/get_data_size.php?data_size_id=' + dataSizeId)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            refreshProductDataSizes(data.data.product_id);
                        } else {
                            console.error('Could not fetch product ID for refreshing:', data.error);
                             // Fallback: Maybe just alert or a partial refresh if possible
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching product ID for refreshing:', error);
                         // Fallback
                    });

            } else {
                alert('Error updating data plan: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Edit Data Size error:', error);
            alert('Failed to update data plan.');
        });
    });

    // Basic htmlspecialchars equivalent for preventing XSS
    function htmlspecialchars(str) {
        if (typeof str !== 'string') {
            return str;
        }
        return str.replace(/&/g, '&amp;')
                  .replace(/</g, '&lt;')
                  .replace(/>/g, '&gt;')
                  .replace(/"/g, '&quot;')
                  .replace(/'/g, '&#039;');
    }

});
</script>

<?php include '../includes/footer.php'; ?> 