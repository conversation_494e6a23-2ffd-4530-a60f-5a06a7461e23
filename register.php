<?php
session_start();
require 'includes/db.php';
require 'includes/email.php';

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $password = $_POST['password'];
    $phone_number = $_POST['phone'];
    
    $stmt = $conn->prepare('SELECT id FROM users WHERE email = ?');
    $stmt->bind_param('s', $email);
    $stmt->execute();
    $stmt->store_result();
    if ($stmt->num_rows > 0) {
        $error = 'Email already exists';
    } else {
        $hash = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare('INSERT INTO users (full_name, email, password, phone_number) VALUES (?, ?, ?, ?)');
        $stmt->bind_param('ssss', $name, $email, $hash, $phone_number);
        $stmt->execute();
        $_SESSION['user'] = [
            'id' => $conn->insert_id,
            'full_name' => $name,
            'email' => $email,
            'account_type' => 'Customer'
        ];
        // Send welcome email
        if (!empty($email)) {
            $subject = 'Welcome to ElsBee Data';
            $msg = '<h3>Welcome, ' . htmlspecialchars($name) . '!</h3>';
            $msg .= '<p>Thank you for registering at ElsBee Data.</p>';
            send_email($email, $subject, $msg);
        }
        header('Location: index.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register - ElsBee Data</title>
  <link href="assets/css/style.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="login-register-bg">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-6 col-lg-5">
        <div class="auth-card">
          <h3><i class="bi bi-person-plus me-2"></i>Create Account</h3>
          <?php if($error): ?>
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-circle me-2"></i><?php echo $error; ?>
            </div>
          <?php endif; ?>
          <form method="post">
            <div class="form-floating mb-3">
              <input type="text" name="name" class="form-control" id="name" placeholder="John Doe" required>
              <label for="name"><i class="bi bi-person me-2"></i>Full Name</label>
            </div>
            <div class="form-floating mb-3">
              <input type="email" name="email" class="form-control" id="email" placeholder="<EMAIL>" required>
              <label for="email"><i class="bi bi-envelope me-2"></i>Email address</label>
            </div>
            <div class="form-floating mb-4">
              <input type="password" name="password" class="form-control" id="password" placeholder="Password" required>
              <label for="password"><i class="bi bi-lock me-2"></i>Password</label>
            </div>
            <div class="form-floating mb-3">
              <input type="tel" name="phone" class="form-control" id="phone" placeholder="e.g., **********" required>
              <label for="phone"><i class="bi bi-phone me-2"></i>Phone Number</label>
            </div>
            <button type="submit" class="btn w-100">
              <i class="bi bi-person-plus me-2"></i>Create Account
            </button>
          </form>
          <div class="auth-links">
            <p class="mb-0">Already have an account? <a href="login.php">Sign In</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 