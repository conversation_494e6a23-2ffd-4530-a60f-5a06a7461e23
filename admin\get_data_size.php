<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();

if ($_SESSION['user']['account_type'] !== 'Admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

$data_size_id = isset($_GET['data_size_id']) ? intval($_GET['data_size_id']) : 0;

if ($data_size_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid data size ID']);
    exit;
}

$stmt = $conn->prepare('SELECT id, size, price, active FROM data_sizes WHERE id = ?');
$stmt->bind_param('i', $data_size_id);
$stmt->execute();
$result = $stmt->get_result();
$data_size = $result->fetch_assoc();

$stmt->close();
$conn->close();

if ($data_size) {
    echo json_encode(['success' => true, 'data' => $data_size]);
} else {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Data size not found']);
}
?> 