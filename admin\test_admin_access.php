<?php
session_start();

// Determine the base path of the application
$base_path = '/';

require '../includes/db.php';
require '../includes/auth.php';
require '../includes/time_manager.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$status_info = get_closure_status_info();
$admin_override = get_admin_closure_override();

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="bi bi-check-circle"></i> Admin Access Test - SUCCESS!</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <strong>✅ Admin access is working correctly!</strong><br>
                        You can access admin pages even when the website is closed.
                    </div>
                    
                    <h6>Current Status:</h6>
                    <ul class="list-group list-group-flush mb-3">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Website Status:</span>
                            <span class="badge <?php echo $status_info['is_closed'] ? 'bg-danger' : 'bg-success'; ?>">
                                <?php echo $status_info['is_closed'] ? 'CLOSED' : 'OPEN'; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Admin Override:</span>
                            <span class="badge <?php echo $admin_override === null ? 'bg-secondary' : 'bg-warning'; ?>">
                                <?php 
                                if ($admin_override === null) echo 'AUTO MODE';
                                elseif ($admin_override === true) echo 'MANUALLY CLOSED';
                                else echo 'MANUALLY OPENED';
                                ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Current Ghana Time:</span>
                            <span><?php echo get_ghana_time_display(); ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Your Account:</span>
                            <span class="badge bg-primary"><?php echo htmlspecialchars($_SESSION['user']['full_name']); ?> (Admin)</span>
                        </li>
                    </ul>
                    
                    <div class="d-grid gap-2">
                        <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-primary">
                            <i class="bi bi-speedometer2"></i> Go to Admin Dashboard
                        </a>
                        <a href="<?php echo $base_path; ?>admin/website_closure.php" class="btn btn-warning">
                            <i class="bi bi-clock"></i> Manage Website Closure
                        </a>
                        <a href="<?php echo $base_path; ?>closed.php" class="btn btn-outline-secondary" target="_blank">
                            <i class="bi bi-eye"></i> Preview Closed Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
