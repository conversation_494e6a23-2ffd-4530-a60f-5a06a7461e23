<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Make sure they're logged in
require_login();

// Only admins can delete reviews from here
if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: /index.php');
    exit;
}

// Get the review ID they want to delete
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Delete the review
$stmt = $conn->prepare('DELETE FROM reviews WHERE id = ?');
$stmt->bind_param('i', $id);
$stmt->execute();

// Send them back to the reviews page with a success message
header('Location: reviews.php?deleted=1');
exit; 