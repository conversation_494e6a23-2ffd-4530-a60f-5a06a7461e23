<?php
function get_payment_button($amount, $order_id, $user_email, $base_path) {
    $paystack_public_key = 'pk_test_b802fc6a9eb929be5d77da0293b97327bf2d71d0'; // TODO: Replace with your real Paystack public key
    $amount_kobo = intval($amount * 100); // Paystack expects amount in kobo/pesewas
    $ref = 'ELS_' . uniqid() . '_' . $order_id;
    
    // Construct the full callback URL using the base_path
    // $callback_url = $base_path . 'cart/success.php?order_id=' . $order_id; // This line was causing the error, removed

    return <<<HTML
<button type="button" class="btn btn-success w-100" onclick="payWithPaystack()">Pay Now</button>
<script src="https://js.paystack.co/v1/inline.js"></script>
<script>
function payWithPaystack() {
    var handler = PaystackPop.setup({
        key: '{$paystack_public_key}',
        email: '{$user_email}',
        amount: {$amount_kobo},
        currency: 'GHS',
        ref: '{$ref}',
        callback: function(response){
            // You should verify the transaction on the server here
            window.location.href = '{$base_path}cart/success.php?order_id={$order_id}&paystack_ref=' + response.reference;
        },
        onClose: function(){
            alert('Payment window closed.');
        }
    });
    handler.openIframe();
}
</script>
HTML;
}
?> 