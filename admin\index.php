<?php
session_start();

// Determine the base path of the application (assuming this file is in a subdirectory)
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

// Check if the logged-in user is an admin
if (isset($_SESSION['user']['account_type']) && $_SESSION['user']['account_type'] === 'Admin') {
    // User is admin, redirect to the dashboard
    header('Location: ' . $base_path . 'admin/dashboard.php');
    exit;
} else {
    // User is logged in but not admin
    // Redirect to home or show an access denied message
    header('Location: ' . $base_path . 'index.php'); // Redirect to home page
    exit;
}

?> 