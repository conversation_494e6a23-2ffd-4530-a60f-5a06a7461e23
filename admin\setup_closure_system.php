<?php
session_start();

// Determine the base path of the application
$base_path = '/';

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$success = $error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
    try {
        // Create settings table if it doesn't exist
        $conn->query("CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // Create activity_logs table if it doesn't exist
        $conn->query("CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");

        // Add indexes
        $conn->query("CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id)");
        $conn->query("CREATE INDEX IF NOT EXISTS idx_activity_logs_action ON activity_logs(action)");
        $conn->query("CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at)");

        // Insert initial settings
        $conn->query("INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES 
            ('closure_schedule_enabled', '1', 'Enable automatic closure schedule'),
            ('closure_start_time', '21:00', 'Time when website closes (24-hour format)'),
            ('closure_end_time', '07:00', 'Time when website opens (24-hour format)')");

        $success = 'Website closure system has been set up successfully! You can now use the admin dashboard and closure management features.';
    } catch (Exception $e) {
        $error = 'Setup failed: ' . $e->getMessage();
    }
}

include 'admin_header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="bi bi-gear"></i> Setup Website Closure System</h4>
                </div>
                <div class="card-body">
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i> <?php echo $success; ?>
                        </div>
                        <div class="d-grid gap-2">
                            <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-primary">
                                <i class="bi bi-speedometer2"></i> Go to Admin Dashboard
                            </a>
                            <a href="<?php echo $base_path; ?>admin/website_closure.php" class="btn btn-warning">
                                <i class="bi bi-clock"></i> Manage Website Closure
                            </a>
                        </div>
                    <?php elseif ($error): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
                        </div>
                    <?php else: ?>
                        <p>This will set up the database tables and initial settings needed for the website closure system.</p>
                        
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> What this setup will do:</h6>
                            <ul class="mb-0">
                                <li>Create <code>settings</code> table for storing configuration</li>
                                <li>Create <code>activity_logs</code> table for tracking admin actions</li>
                                <li>Add necessary database indexes for performance</li>
                                <li>Insert initial closure system settings</li>
                            </ul>
                        </div>
                        
                        <form method="post">
                            <div class="d-grid">
                                <button type="submit" name="setup" class="btn btn-primary btn-lg">
                                    <i class="bi bi-play-circle"></i> Run Setup
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-list-check"></i> System Status Check</h6>
                </div>
                <div class="card-body">
                    <?php
                    // Check if tables exist
                    $settings_exists = false;
                    $activity_logs_exists = false;
                    
                    try {
                        $result = $conn->query("SHOW TABLES LIKE 'settings'");
                        $settings_exists = $result->num_rows > 0;
                        
                        $result = $conn->query("SHOW TABLES LIKE 'activity_logs'");
                        $activity_logs_exists = $result->num_rows > 0;
                    } catch (Exception $e) {
                        // Ignore errors
                    }
                    ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-<?php echo $settings_exists ? 'check-circle text-success' : 'x-circle text-danger'; ?> me-2"></i>
                                <span>Settings Table: <?php echo $settings_exists ? 'Exists' : 'Missing'; ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-<?php echo $activity_logs_exists ? 'check-circle text-success' : 'x-circle text-danger'; ?> me-2"></i>
                                <span>Activity Logs Table: <?php echo $activity_logs_exists ? 'Exists' : 'Missing'; ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($settings_exists && $activity_logs_exists): ?>
                        <div class="alert alert-success mt-3 mb-0">
                            <i class="bi bi-check-circle"></i> All required tables exist! The closure system should work properly.
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning mt-3 mb-0">
                            <i class="bi bi-exclamation-triangle"></i> Some tables are missing. Please run the setup to create them.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="mt-3">
                <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php include 'admin_footer.php'; ?>
