<?php

// Function to log admin activity
function log_admin_activity($conn, $user_id, $action_type, $action_description) {
    // Ensure connection and user ID are valid
    if (!$conn || !isset($user_id)) {
        // In a real application, you might log this failure to a separate system log
        error_log("Failed to log activity: Database connection or user ID missing.");
        return false;
    }

    // Prepare and execute the INSERT statement
    $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action_type, action_description) VALUES (?, ?, ?)");

    // Check if statement preparation was successful
    if ($stmt === false) {
        error_log("Failed to prepare activity log statement: " . $conn->error);
        return false;
    }

    // Bind parameters and execute
    $stmt->bind_param("iss", $user_id, $action_type, $action_description);
    $success = $stmt->execute();

    // Log error if execution failed
    if ($success === false) {
        error_log("Failed to execute activity log statement: " . $stmt->error);
    }

    $stmt->close();

    return $success;
}

?> 