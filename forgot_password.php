<?php
session_start();
require 'includes/db.php';
require 'includes/email.php'; // Assuming you have an email sending function

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Check if the email exists in the database
        $stmt = $conn->prepare('SELECT id FROM users WHERE email = ?');
        $stmt->bind_param('s', $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();

        if ($user) {
            // Generate a unique token
            $token = bin2hex(random_bytes(32));
            // Set token expiration time (e.g., 1 hour)
            $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // Store the token and expiration in the database
            // You might need to add columns like 'reset_token' and 'reset_expires_at' to your users table
            $stmt = $conn->prepare('UPDATE users SET reset_token = ?, reset_expires_at = ? WHERE id = ?');
            $stmt->bind_param('ssi', $token, $expires_at, $user['id']);
            $stmt->execute();

            // Send password reset email
            // Construct the local reset link using the base path
            // Updated for live server and root path
            $base_path = '/'; // Make sure this matches your actual base path configuration
            $reset_link = "http://elsbeedata.site" . $base_path . "reset_password.php?token=" . $token; // Use domain and base path

            $subject = 'Password Reset Request';
            $msg = '<h3>Password Reset</h3>';
            $msg .= '<p>You have requested to reset your password. Click the link below to reset it:</p>';
            $msg .= '<p><a href="' . $reset_link . '">' . $reset_link . '</a></p>';
            $msg .= '<p>This link will expire in 1 hour.</p>';
            $msg .= '<p>If you did not request a password reset, please ignore this email.</p>';

            // Specify a From address for the email
            $from_email = '<EMAIL>'; // Replace with a valid email address for your domain
            $from_name = 'ElsBee Data';
            $from_header = "{$from_name} <{$from_email}>";

            if (send_email($email, $subject, $msg, $from_header)) {
                $success = 'A password reset link has been sent to your email address.';
            } else {
                $error = 'Failed to send password reset email.';
            }
        } else {
            // If email doesn't exist, still show a success message to prevent user enumeration
            $success = 'If a user with that email address exists, a password reset link has been sent.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Forgot Password - ElsBee Data</title>
  <link href="assets/css/style.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="login-register-bg">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-6 col-lg-5">
        <div class="auth-card">
          <h3><i class="bi bi-lock-reset me-2"></i>Forgot Password</h3>
          <?php if($error): ?>
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
            </div>
          <?php endif; ?>
          <?php if($success): ?>
            <div class="alert alert-success">
              <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
            </div>
          <?php endif; ?>
          <form method="post">
            <div class="form-floating mb-3">
              <input type="email" name="email" class="form-control" id="email" placeholder="<EMAIL>" required>
              <label for="email"><i class="bi bi-envelope me-2"></i>Email address</label>
            </div>
            <button type="submit" class="btn w-100">
              <i class="bi bi-send me-2"></i>Send Reset Link
            </button>
          </form>
          <div class="auth-links">
            <p class="mb-0"><a href="login.php">Back to Login</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 