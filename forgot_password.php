<?php
session_start();
require 'includes/db.php';
require 'includes/email.php'; // Assuming you have an email sending function

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Check if the email exists in the database
        $stmt = $conn->prepare('SELECT id FROM users WHERE email = ?');
        $stmt->bind_param('s', $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();

        if ($user) {
            // Generate a unique token
            $token = bin2hex(random_bytes(32));
            // Set token expiration time (e.g., 1 hour)
            $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // Store the token and expiration in the database
            // You might need to add columns like 'reset_token' and 'reset_expires_at' to your users table
            $stmt = $conn->prepare('UPDATE users SET reset_token = ?, reset_expires_at = ? WHERE id = ?');
            $stmt->bind_param('ssi', $token, $expires_at, $user['id']);
            $stmt->execute();

            // Send password reset email
            // Construct the reset link using HTTPS
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'https'; // Force HTTPS
            $host = $_SERVER['HTTP_HOST'] ?? 'elsbeedata.site';
            $reset_link = $protocol . "://" . $host . "/reset_password.php?token=" . $token;

            $subject = 'ElsBee Data - Password Reset Request';

            // Create a professional HTML email
            $msg = '
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Password Reset</title>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #6a6ee7; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                    .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
                    .button { display: inline-block; background: #6a6ee7; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                    .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>🔐 Password Reset Request</h2>
                    </div>
                    <div class="content">
                        <p>Hello,</p>
                        <p>You have requested to reset your password for your ElsBee Data account.</p>
                        <p>Click the button below to reset your password:</p>
                        <p style="text-align: center;">
                            <a href="' . $reset_link . '" class="button">Reset My Password</a>
                        </p>
                        <p>Or copy and paste this link into your browser:</p>
                        <p style="word-break: break-all; background: #eee; padding: 10px; border-radius: 4px;">
                            ' . $reset_link . '
                        </p>
                        <p><strong>Important:</strong></p>
                        <ul>
                            <li>This link will expire in 1 hour</li>
                            <li>If you did not request this reset, please ignore this email</li>
                            <li>Your password will not be changed until you click the link above</li>
                        </ul>
                        <p>If you have any questions, please contact our support team.</p>
                        <p>Best regards,<br>The ElsBee Data Team</p>
                    </div>
                    <div class="footer">
                        <p>ElsBee Data - Your trusted data bundle provider in Ghana</p>
                        <p>This email was sent to ' . htmlspecialchars($email) . '</p>
                    </div>
                </div>
            </body>
            </html>';

            // Specify a From address for the email
            $from_header = 'ElsBee Data <<EMAIL>>';

            // Log the attempt
            error_log("Attempting to send password reset email to: " . $email);

            if (send_email($email, $subject, $msg, $from_header)) {
                $success = 'A password reset link has been sent to your email address. Please check your inbox and spam folder.';
                error_log("Password reset email sent successfully to: " . $email);
            } else {
                $error = 'Failed to send password reset email. Please try again or contact support.';
                error_log("Failed to send password reset email to: " . $email);
            }
        } else {
            // If email doesn't exist, still show a success message to prevent user enumeration
            $success = 'If a user with that email address exists, a password reset link has been sent.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Forgot Password - ElsBee Data</title>
  <link href="assets/css/style.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="login-register-bg">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-6 col-lg-5">
        <div class="auth-card">
          <h3><i class="bi bi-lock-reset me-2"></i>Forgot Password</h3>
          <?php if($error): ?>
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
            </div>
          <?php endif; ?>
          <?php if($success): ?>
            <div class="alert alert-success">
              <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
            </div>
          <?php endif; ?>
          <form method="post">
            <div class="form-floating mb-3">
              <input type="email" name="email" class="form-control" id="email" placeholder="<EMAIL>" required>
              <label for="email"><i class="bi bi-envelope me-2"></i>Email address</label>
            </div>
            <button type="submit" class="btn w-100">
              <i class="bi bi-send me-2"></i>Send Reset Link
            </button>
          </form>
          <div class="auth-links">
            <p class="mb-0"><a href="login.php">Back to Login</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 