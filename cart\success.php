<?php
session_start();
require '../includes/db.php'; // Include database connection
include '../includes/header.php';
// require '../includes/email.php'; // Include email function (not needed for this page anymore)

$order_id = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
// $paystack_ref = isset($_GET['paystack_ref']) ? $_GET['paystack_ref'] : ''; // Not needed here

// $user_email = $_SESSION['user']['email'] ?? ''; // Not needed here

// Fetch order details and items (still needed to display order ID and potentially manual payment instructions)
$order_details = null;
$order_items = [];
$order_total = 0;

if ($order_id > 0) {
    // Fetch order details (including total and user_id)
    $order_stmt = $conn->prepare('SELECT total, user_id FROM orders WHERE id = ?');
    $order_stmt->bind_param('i', $order_id);
    $order_stmt->execute();
    $order_result = $order_stmt->get_result();
    $order_details = $order_result->fetch_assoc();

    if ($order_details) {
        $order_total = $order_details['total'];

        // Fetch order items to check for AFA bundles for instructions display
        $order_items_stmt = $conn->prepare('SELECT oi.*, p.name FROM order_items oi JOIN products p ON oi.product_id = p.id WHERE order_id = ?');
        $order_items_stmt->bind_param('i', $order_id);
        $order_items_stmt->execute();
        $order_items_result = $order_items_stmt->get_result();
        while ($item = $order_items_result->fetch_assoc()) {
            $order_items[] = $item;
        }
    }
}

?>
<div class="container py-5 text-center">
  <div class="alert alert-success">
    <h3>Thank you for your order!</h3>
    <p>Your order (ID: <?php echo $order_id; ?>) has been placed successfully.</p>
    <!-- Updated message to remove email confirmation -->
    <p>You will receive a confirmation once your order has been processed.</p>
    <?php
    // Display manual payment instructions for MTN AFA if applicable, even without email
    $is_mtnafa_order = false;
    foreach ($order_items as $item) {
        if (strpos($item['name'], 'MTN AFA') !== false) { // Check if any item name contains 'MTN AFA'
            $is_mtnafa_order = true;
            break;
        }
    }

    if ($is_mtnafa_order && $order_details) {
        ?>
        <hr>
        <h4>Manual Payment Instructions for MTN AFA Bundle:</h4>
        <p>Please send the total amount of <strong>&#8373;<?php echo number_format($order_total, 2); ?></strong> to the following Mobile Money number:</p>
        <p><strong>[Your MTN Merchant Number Here]</strong> (e.g., 055XXXXXXX)</p> <!-- TODO: Replace with actual merchant number or provide instructions on where to find it -->
        <p>Use your Order ID <strong>#<?php echo $order_id; ?></strong> or Reference Code (if available) as the payment reference.</p>
        <p>Once payment is confirmed, your AFA bundle will be processed.</p>
        <p><strong>Important:</strong> Please allow some time for manual payment verification.</p>
        <?php
    }
    ?>
    <a href="../orders/index.php" class="btn btn-primary mt-3">View My Orders</a>
  </div>
</div>
<?php include '../includes/footer.php'; ?> 