<?php
session_start();
require 'includes/db.php';
include 'includes/header.php';

$network = 'MTN';

// Handle Add to Cart form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['data_size_id'])) {
    // Add to cart logic (if any is still here, though it's mostly AJAX now)
    // This block might be empty or contain old logic that can be removed if AJAX handles everything.
    // However, adding the check for $_POST['data_size_id'] prevents the review_text error.
}

// Fetch product details and the min/max prices from associated data sizes
$stmt = $conn->prepare('
    SELECT p.*,
           MIN(ds.price) as min_price,
           MAX(ds.price) as max_price
    FROM products p
    LEFT JOIN data_sizes ds ON p.id = ds.product_id AND ds.active = 1
    WHERE p.network = ? AND p.active = 1
    GROUP BY p.id
    LIMIT 1
');
$stmt->bind_param('s', $network);
$stmt->execute();
$product_result = $stmt->get_result();
$product = $product_result->fetch_assoc();

if (!$product || ($product['min_price'] === null && $product['max_price'] === null)): // Also check if min/max prices are null, meaning no active data sizes
?>
<div class="container py-5">
  <div class="alert alert-danger">
    <i class="bi bi-exclamation-circle me-2"></i>No MTN products found or available.
  </div>
  <div class="text-center mt-3">
    <a href="index.php" class="btn btn-primary">Back to Home</a>
  </div>
</div>
<?php include 'includes/footer.php'; exit; endif; ?>

<?php
// Fetch data sizes for this product
$data_sizes_stmt = $conn->prepare('SELECT id, size, price FROM data_sizes WHERE product_id = ? AND active = 1 ORDER BY price ASC');
$data_sizes_stmt->bind_param('i', $product['id']);
$data_sizes_stmt->execute();
$data_sizes_result = $data_sizes_stmt->get_result();

?>

<div class="container py-5">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="<?php echo '/'; ?>index.php">Home</a></li>
      <li class="breadcrumb-item"><a href="#"><?php echo $network; ?></a></li>
      <li class="breadcrumb-item active" aria-current="page"><?php echo $network; ?> Data Bundles</li>
    </ol>
  </nav>
  <div class="row">
    <div class="col-md-5 text-center mb-4 mb-md-0">
      <img src="https://upload.wikimedia.org/wikipedia/commons/9/93/New-mtn-logo.jpg" class="img-fluid" alt="<?php echo $network; ?> Logo">
    </div>
    <div class="col-md-7">
      <h2><?php echo htmlspecialchars($product['name']); ?></h2>
      <?php if($product['min_price'] !== null): // Only display price range if there are active data sizes ?>
        <h4 class="text-success">&#8373;<?php echo number_format($product['min_price'],2); ?> - &#8373;<?php echo number_format($product['max_price'],2); ?></h4>
      <?php else: ?>
        <h4 class="text-warning">Prices not available</h4>
      <?php endif; ?>
      <p class="text-success">+ Free Shipping</p>
      <p class="text-danger fw-bold">MAKE SURE NUMBER RECEIVING DATA DOESN'T OWE AIRTIME ELSE DATA WON'T BE RECEIVED AND MONEY NON REFUNDABLE !!!</p>
      <p>All data packages are non expiry.</p>
      <?php if(isset($_SESSION['user'])): ?>
        <form id="mtn-product-form" method="post" action="<?php echo '/'; ?>cart/add.php">
          <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
          <div class="mb-3">
            <label class="form-label">DATA SIZE</label>
            <select name="data_size_id" class="form-select" required>
              <option value="">Choose an option</option>
              <?php
              while ($size_option = $data_sizes_result->fetch_assoc()):
              ?>
                <option value="<?php echo htmlspecialchars($size_option['id']); ?>"><?php echo htmlspecialchars($size_option['size']) . ' - GHS' . htmlspecialchars(number_format($size_option['price'], 2)); ?></option>
              <?php endwhile; ?>
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">Quantity</label>
            <input type="number" name="quantity" class="form-control" value="1" min="1" max="10" required>
          </div>
          <div class="mb-3">
            <label for="beneficiary_number" class="form-label">Beneficiary Number</label>
            <input type="text" class="form-control" id="beneficiary_number" name="beneficiary_number" placeholder="e.g., 05XXXXXXXX" required>
          </div>
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-cart-plus me-2"></i>ADD TO CART
          </button>
        </form>
      <?php else: ?>
        <div class="alert alert-info">
          <i class="bi bi-info-circle me-2"></i>Please <a href="login.php?redirect=<?php echo urlencode($_SERVER['REQUEST_URI']); ?>" class="alert-link">login</a> to purchase this product.
        </div>
      <?php endif; ?>
      
    </div>
  </div>
  <div class="row mt-5">
    <div class="col-md-8">
      <ul class="nav nav-tabs" id="descTab" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="desc-tab" data-bs-toggle="tab" data-bs-target="#desc" type="button" role="tab">Additional information</button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab">Description</button>
        </li>
      </ul>
      <div class="tab-content p-3 border border-top-0" id="descTabContent">
        <div class="tab-pane fade show active" id="desc" role="tabpanel">
          <?php echo nl2br(htmlspecialchars($product['description'])); ?>
          
          <h6 class="mt-4">Important Notices:</h6>
          <ul>
            <li>We do not accept orders for MTN with the under-listed numbers:</li>
            <ul>
              <li>Turbonet/Router SIMs</li>
              <li>Broadband SIMs</li>
              <li>Agent SIMs</li>
              <li>Merchant SIMs</li>
              <li>Blacklisted SIMs</li>
            </ul>
          </ul>
          <h6 class="mt-4">Refund Policy Notices:</h6>
          <ul>
            <li>No refund for the following errors at your end:</li>
            <ul>
              <li>Numbers less than 10 digit</li>
              <li>Wrong recipient/number</li>
              <li>Placing Mtn data for Tigo number and vice versa.</li>
            </ul>
          </ul>
        </div>
        <div class="tab-pane fade" id="info" role="tabpanel">
          MTN Data Bundles: 1GB, 2GB, 3GB, 4GB, 5GB, 6GB, 8GB, 10GB, 15GB, 20GB, 25GB, 30GB, 35GB, 40GB, 45GB, 50GB, 100GB
        </div>
      </div>
    </div>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add to cart form handler (existing)
    const cartForm = document.querySelector('#mtn-product-form');
    if (cartForm) {
        cartForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(cartForm);

            fetch('<?php echo '/'; ?>cart/add.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAlert(data.message, 'success');
                    const cartCountElement = document.querySelector('.cart-count');
                    if (cartCountElement) {
                        cartCountElement.innerText = data.cart_count;
                    }
                } else {
                    displayAlert('Error adding item to cart: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                displayAlert('An unexpected error occurred. Please try again.', 'danger');
            });
        });
    }
});
</script> 
