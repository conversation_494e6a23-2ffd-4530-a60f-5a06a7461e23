<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();
if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: /index.php');
    exit;
}
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$stmt = $conn->prepare('SELECT * FROM users WHERE id = ?');
$stmt->bind_param('i', $id);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();
if (!$user) {
    header('Location: users.php');
    exit;
}
$success = $error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $type = $_POST['account_type'];
    $stmt = $conn->prepare('UPDATE users SET full_name = ?, email = ?, account_type = ? WHERE id = ?');
    $stmt->bind_param('sssi', $name, $email, $type, $id);
    if ($stmt->execute()) {
        header('Location: users.php?success=1');
        exit;
    } else {
        $error = 'Failed to update user.';
    }
}
include '../includes/header.php';
?>
<div class="container py-5">
  <h2 class="mb-4">Edit User</h2>
  <?php if($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>
  <form method="post" class="card p-4">
    <div class="mb-3">
      <label>Full Name</label>
      <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
    </div>
    <div class="mb-3">
      <label>Email</label>
      <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
    </div>
    <div class="mb-3">
      <label>Account Type</label>
      <select name="account_type" class="form-select" required>
        <option value="Customer" <?php if($user['account_type']==='Customer') echo 'selected'; ?>>Customer</option>
        <option value="Agent" <?php if($user['account_type']==='Agent') echo 'selected'; ?>>Agent</option>
        <option value="Admin" <?php if($user['account_type']==='Admin') echo 'selected'; ?>>Admin</option>
      </select>
    </div>
    <button type="submit" class="btn btn-primary">Update User</button>
    <a href="users.php" class="btn btn-secondary ms-2">Cancel</a>
  </form>
</div>
<?php include '../includes/footer.php'; ?> 