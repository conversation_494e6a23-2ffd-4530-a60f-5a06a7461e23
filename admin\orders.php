<?php
session_start();

// Determine the base path of the application (assuming this file is in a subdirectory)
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}
$orders = $conn->query('SELECT o.*, u.full_name FROM orders o JOIN users u ON o.user_id = u.id ORDER BY o.created_at DESC');
include '../includes/header.php';
?>
<div class="container py-5">
  <h2 class="mb-4">Manage Orders</h2>
  <div class="table-responsive">
    <table class="table table-bordered table-striped align-middle">
      <thead>
        <tr>
          <th>Order ID</th>
          <th>Reference Code</th>
          <th>Customer</th>
          <th>Number</th>
          <th>Total</th>
          <th>Status</th>
          <th>Payment Status</th>
          <th>Order Date</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
      <?php while($order = $orders->fetch_assoc()): ?>
        <tr>
          <td><?php echo $order['id']; ?></td>
          <td>
            <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($order['reference_code']); ?></code>
          </td>
          <td><?php echo htmlspecialchars($order['full_name']); ?></td>
          <td><?php echo htmlspecialchars($order['user_number'] ?: 'N/A'); ?></td>
          <td>&#8373;<?php echo number_format($order['total'], 2); ?></td>
          <td>
            <?php
              $status = $order['status'];
              $badge_class = '';
              switch ($status) {
                case 'Pending':
                  $badge_class = 'bg-warning';
                  break;
                case 'Completed':
                  $badge_class = 'bg-success';
                  break;
                case 'Cancelled':
                  $badge_class = 'bg-danger';
                  break;
                default:
                  $badge_class = 'bg-secondary';
              }
            ?>
            <span class="badge <?php echo $badge_class; ?>"><?php echo htmlspecialchars($status); ?></span>
          </td>
          <td>
             <?php
              $payment_status = $order['payment_status'];
              $badge_class = '';
              switch ($payment_status) {
                case 'Awaiting Manual Payment':
                  $badge_class = 'bg-warning';
                  break;
                case 'PAID':
                  $badge_class = 'bg-success';
                  break;
                case 'FAILED':
                  $badge_class = 'bg-danger';
                  break;
                default:
                  $badge_class = 'bg-secondary';
              }
            ?>
            <span class="badge <?php echo $badge_class; ?>"><?php echo htmlspecialchars($payment_status); ?></span>
          </td>
          <td><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></td>
          <td><a href="view_order.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-primary">View</a></td>
        </tr>
      <?php endwhile; ?>
      </tbody>
    </table>
  </div>
  <?php if (isset($base_path)): ?>
  <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary mt-3">Back to Dashboard</a>
  <?php endif; ?>
</div>
<?php include '../includes/footer.php'; ?> 
