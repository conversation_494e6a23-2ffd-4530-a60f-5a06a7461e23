</div> <!-- Close content div -->

<!-- Admin <PERSON>er -->
<footer class="bg-light border-top mt-5 py-3">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-md-6">
        <small class="text-muted">
          <i class="bi bi-shield-check"></i> ElsBee Data Admin Panel
        </small>
      </div>
      <div class="col-md-6 text-md-end">
        <small class="text-muted">
          <i class="bi bi-clock"></i> <span id="admin-current-time"><?php echo date('Y-m-d H:i:s'); ?></span>
        </small>
      </div>
    </div>
  </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Update admin time every second
function updateAdminTime() {
    const now = new Date();
    document.getElementById('admin-current-time').textContent = now.toLocaleString();
}

setInterval(updateAdminTime, 1000);

// Admin-specific functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation to dangerous actions
    document.querySelectorAll('.btn-danger, .delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (!this.hasAttribute('data-no-confirm')) {
                if (!confirm('Are you sure you want to perform this action?')) {
                    e.preventDefault();
                }
            }
        });
    });
});
</script>

</body>
</html>
