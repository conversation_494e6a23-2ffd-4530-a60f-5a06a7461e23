<?php
session_start();

// Determine the base path of the application
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$success = $error = '';

// Fetch existing settings
$settings = [];
$result = $conn->query("SELECT setting_key, setting_value, description FROM settings");
while ($row = $result->fetch_assoc()) {
    $settings[$row['setting_key']] = ['value' => $row['setting_value'], 'description' => $row['description']];
}

// Handle POST request to save settings
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $conn->begin_transaction();
    try {
        // Clear existing settings to re-insert
        // $conn->query("DELETE FROM settings"); // Consider if you want to delete and re-add or update

        $update_stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), description = VALUES(description)");

        foreach ($_POST['settings'] as $key => $data) {
            $value = $data['value'];
            // Keep existing description if not provided in form (assuming form doesn't send it)
            // For simplicity here, we are re-using the fetched description or setting it to null
            $description = $settings[$key]['description'] ?? null; // Re-use existing description if it exists and isn't being sent by form

            $update_stmt->bind_param("sss", $key, $value, $description);
            $update_stmt->execute();
        }

        $conn->commit();
        $success = 'Settings updated successfully.';

        // Re-fetch settings after save to display updated values
        $settings = [];
        $result = $conn->query("SELECT setting_key, setting_value, description FROM settings");
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = ['value' => $row['setting_value'], 'description' => $row['description']];
        }

    } catch (Exception $e) {
        $conn->rollback();
        $error = 'Failed to update settings: ' . $e->getMessage();
    }
}

include '../includes/header.php';
?>

<div class="container py-5">
    <h2 class="mb-4">System Settings</h2>

    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <form method="post">
        <div class="card p-4">
            <h4>General Settings</h4>
            <div class="mb-3">
                <label for="site_name" class="form-label">Site Name</label>
                <input type="text" class="form-control" id="site_name" name="settings[site_name][value]" value="<?php echo htmlspecialchars($settings['site_name']['value'] ?? ''); ?>">
                <small class="form-text text-muted"><?php echo htmlspecialchars($settings['site_name']['description'] ?? 'The main name of your website.'); ?></small>
            </div>
            <div class="mb-3">
                <label for="contact_email" class="form-label">Contact Email</label>
                <input type="email" class="form-control" id="contact_email" name="settings[contact_email][value]" value="<?php echo htmlspecialchars($settings['contact_email']['value'] ?? ''); ?>">
                <small class="form-text text-muted"><?php echo htmlspecialchars($settings['contact_email']['description'] ?? 'Email address for customer inquiries.'); ?></small>
            </div>
            <div class="mb-3">
                <label for="site_currency_symbol" class="form-label">Currency Symbol</label>
                <input type="text" class="form-control" id="site_currency_symbol" name="settings[site_currency_symbol][value]" value="<?php echo htmlspecialchars($settings['site_currency_symbol']['value'] ?? ''); ?>">
                 <small class="form-text text-muted"><?php echo htmlspecialchars($settings['site_currency_symbol']['description'] ?? 'The currency symbol to display (e.g., &#8373;).'); ?></small>
            </div>
            
            <!-- Add more setting fields here as needed -->

            <button type="submit" class="btn btn-primary">Save Settings</button>
        </div>
    </form>

    <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary mt-3">Back to Dashboard</a>
</div>

<?php include '../includes/footer.php'; ?> 