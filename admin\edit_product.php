<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();
if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: /index.php');
    exit;
}
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$stmt = $conn->prepare('SELECT * FROM products WHERE id = ?');
$stmt->bind_param('i', $id);
$stmt->execute();
$product = $stmt->get_result()->fetch_assoc();
if (!$product) {
    header('Location: products.php');
    exit;
}
$success = $error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if updating main product details
    if (isset($_POST['update_product'])) {
        $name = $_POST['name'];
        $desc = $_POST['description'];
        $network = $_POST['network'];
        $active = isset($_POST['active']) ? 1 : 0;
        $stmt = $conn->prepare('UPDATE products SET name=?, description=?, network=?, active=? WHERE id=?');
        $stmt->bind_param('sssii', $name, $desc, $network, $active, $id);
        if ($stmt->execute()) {
            $success = 'Product details updated successfully.';
             // Redirect to prevent form resubmission on refresh, but stay on edit page
            header("Location: edit_product.php?id=$id&success=" . urlencode($success));
            exit();
        } else {
            $error = 'Failed to update product details.';
        }
    }

    // Check if adding a new data size
    if (isset($_POST['add_data_size'])) {
        $product_id = $_POST['product_id'];
        $size = $_POST['new_size'];
        $price = floatval($_POST['new_price']);
        $active = isset($_POST['new_active']) ? 1 : 0;

        $stmt = $conn->prepare('INSERT INTO data_sizes (product_id, size, price, active) VALUES (?, ?, ?, ?)');
        $stmt->bind_param('isdi', $product_id, $size, $price, $active);

        if ($stmt->execute()) {
            $success = 'Data size added successfully.';
             // Redirect to prevent form resubmission on refresh
            header("Location: edit_product.php?id=$id&success=" . urlencode($success));
            exit();
        } else {
            $error = 'Failed to add data size.';
        }
    }

     // Check if updating existing data sizes
    if (isset($_POST['update_data_sizes']) && isset($_POST['data_sizes'])) {
        $updated_count = 0;
        $conn->begin_transaction(); // Start transaction

        try {
            $stmt = $conn->prepare('UPDATE data_sizes SET size=?, price=?, active=? WHERE id=?');

            foreach ($_POST['data_sizes'] as $data_size_data) {
                $data_size_id = $data_size_data['id'];
                $size = $data_size_data['size'];
                $price = floatval($data_size_data['price']);
                // Check if the checkbox was sent (meaning it's checked). If not, it's 0.
                $active = isset($data_size_data['active']) ? 1 : 0;

                $stmt->bind_param('sdis', $size, $price, $active, $data_size_id);
                $stmt->execute();
                // Check for errors on individual execute if needed, or rely on transaction rollback
                $updated_count++;
            }

            $conn->commit(); // Commit transaction
            $success = "Updated {$updated_count} data size(s) successfully.";
             // Redirect to prevent form resubmission on refresh
             header("Location: edit_product.php?id=$id&success=" . urlencode($success));
             exit();

        } catch (Exception $e) {
            $conn->rollback(); // Rollback transaction on error
            $error = 'Failed to update data sizes: ' . $e->getMessage();
        }
    }

} else if (isset($_GET['remove_size_id'])) {
    // Handle data size removal
    $remove_size_id = intval($_GET['remove_size_id']);
    
    // Optional: Add a check here to ensure the data size belongs to the current product ID
    // $check_stmt = $conn->prepare('SELECT product_id FROM data_sizes WHERE id = ?');
    // $check_stmt->bind_param('i', $remove_size_id);
    // $check_stmt->execute();
    // $size_product_id = $check_stmt->get_result()->fetch_assoc()['product_id'] ?? null;
    // if ($size_product_id != $id) {
    //     $error = 'Unauthorized attempt to remove data size.';
    // } else {
        // Before deleting, check if this data size is used in any order_items
        $check_order_items_stmt = $conn->prepare('SELECT COUNT(*) FROM order_items WHERE data_size = (SELECT size FROM data_sizes WHERE id = ? LIMIT 1)');
        $check_order_items_stmt->bind_param('i', $remove_size_id);
        $check_order_items_stmt->execute();
        $order_item_count = $check_order_items_stmt->get_result()->fetch_row()[0];

        if ($order_item_count > 0) {
            $error = 'Cannot remove data size because it is associated with existing orders.';
        } else {
            // Perform the deletion
            $delete_stmt = $conn->prepare('DELETE FROM data_sizes WHERE id = ?');
            $delete_stmt->bind_param('i', $remove_size_id);
            if ($delete_stmt->execute()) {
                 $success = 'Data size removed successfully.';
                 // Redirect back to the edit page to refresh the list
                 header("Location: edit_product.php?id=$id&success=" . urlencode($success));
                 exit();
            } else {
                 $error = 'Failed to remove data size.';
            }
        }
    // }

}

// Fetch data sizes for this product (re-fetch after potential updates/additions/removals)
$data_sizes_stmt = $conn->prepare('SELECT id, size, price, active FROM data_sizes WHERE product_id = ? ORDER BY price ASC');
$data_sizes_stmt->bind_param('i', $id);
$data_sizes_stmt->execute();
$data_sizes = $data_sizes_stmt->get_result()->fetch_all(MYSQLI_ASSOC);

// Handle success/error messages from redirect
if(isset($_GET['success'])) {
    $success = htmlspecialchars($_GET['success']);
}
if(isset($_GET['error'])) {
    $error = htmlspecialchars($_GET['error']);
}

include '../includes/header.php';
?>
<div class="container py-5">
  <h2 class="mb-4">Edit Product</h2>
  <?php if($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>
   <?php if($success): ?><div class="alert alert-success"><?php echo $success; ?></div><?php endif; ?>

  <form method="post" class="card p-4">
    <input type="hidden" name="update_product" value="1"> <!-- Hidden field to identify this form submission -->
    <div class="mb-3">
      <label>Name</label>
      <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($product['name']); ?>" required>
    </div>
    <div class="mb-3">
      <label>Description</label>
      <textarea name="description" class="form-control" rows="3" required><?php echo htmlspecialchars($product['description']); ?></textarea>
    </div>
    <div class="mb-3">
      <label>Network</label>
      <select name="network" class="form-select" required>
        <option value="MTN" <?php if($product['network']==='MTN') echo 'selected'; ?>>MTN</option>
        <option value="Airtel" <?php if($product['network']==='Airtel') echo 'selected'; ?>>Airtel</option>
        <option value="Telecel" <?php if($product['network']==='Telecel') echo 'selected'; ?>>Telecel</option>
        <option value="MTN AFA" <?php if($product['network']==='MTN AFA') echo 'selected'; ?>>MTN AFA</option>
        <option value="AirtelTigo" <?php if($product['network']==='AirtelTigo') echo 'selected'; ?>>AirtelTigo</option>
      </select>
    </div>
    <div class="form-check mb-3">
      <input class="form-check-input" type="checkbox" name="active" id="active" <?php if($product['active']) echo 'checked'; ?>>
      <label class="form-check-label" for="active">Active</label>
    </div>
    <button type="submit" name="update_product" class="btn btn-primary">Update Product</button>
    <a href="products.php" class="btn btn-secondary ms-2">Cancel</a>
  </form>

  <h3 class="mt-5">Data Sizes</h3>
  <!-- Updated form action to handle updates, additions, and removals -->
  <form method="post" class="card p-4 mt-3">
    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Size</th>
          <th>Price (GHS)</th>
          <th>Active</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
        <?php if (empty($data_sizes)): ?>
          <tr>
            <td colspan="4" class="text-center">No data sizes found for this product.</td>
          </tr>
        <?php else: ?>
          <?php foreach ($data_sizes as $size): ?>
            <tr>
              <td>
                <input type="hidden" name="data_sizes[<?php echo $size['id']; ?>][id]" value="<?php echo $size['id']; ?>">
                <input type="text" name="data_sizes[<?php echo $size['id']; ?>][size]" class="form-control" value="<?php echo htmlspecialchars($size['size']); ?>" required>
              </td>
              <td>
                <input type="number" step="0.01" name="data_sizes[<?php echo $size['id']; ?>][price]" class="form-control" value="<?php echo $size['price']; ?>" required>
              </td>
              <td class="text-center">
                <input type="checkbox" name="data_sizes[<?php echo $size['id']; ?>][active]" value="1" <?php if($size['active']) echo 'checked'; ?> class="form-check-input">
              </td>
              <td>
                <button type="button" class="btn btn-danger btn-sm remove-data-size" data-id="<?php echo $size['id']; ?>"><i class="bi bi-trash"></i> Remove</button>
              </td>
            </tr>
          <?php endforeach; ?>
        <?php endif; ?>
      </tbody>
    </table>
     <button type="submit" name="update_data_sizes" class="btn btn-primary">Update Data Sizes</button>
  </form>

   <h3 class="mt-5">Add New Data Size</h3>
  <form method="post" class="card p-4 mt-3">
    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
    <div class="row g-3">
      <div class="col-md-4">
        <label for="new_size" class="form-label">Size</label>
        <input type="text" class="form-control" id="new_size" name="new_size" required>
      </div>
      <div class="col-md-4">
        <label for="new_price" class="form-label">Price (GHS)</label>
        <input type="number" step="0.01" class="form-control" id="new_price" name="new_price" required>
      </div>
       <div class="col-md-2 d-flex align-items-end">
         <div class="form-check mb-3">
           <input class="form-check-input" type="checkbox" name="new_active" id="new_active" value="1" checked>
           <label class="form-check-label" for="new_active">Active</label>
         </div>
       </div>
      <div class="col-md-2 d-flex align-items-end">
        <button type="submit" name="add_data_size" class="btn btn-success w-100"><i class="bi bi-plus-circle"></i> Add Size</button>
      </div>
    </div>
  </form>

</div>

<!-- Modal for Remove Confirmation -->
<div class="modal fade" id="confirmRemoveSizeModal" tabindex="-1" aria-labelledby="confirmRemoveSizeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-sm">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="confirmRemoveSizeModalLabel">Confirm Removal</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Are you sure you want to remove this data size? This action cannot be undone.
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmRemoveSizeBtn">Remove</button>
      </div>
    </div>
  </div>
</div>

<?php include '../includes/footer.php'; ?>

<script>
 document.addEventListener('DOMContentLoaded', function() {
    const removeSizeModal = new bootstrap.Modal(document.getElementById('confirmRemoveSizeModal'));
    let dataSizeToRemove = null;

    document.querySelectorAll('.remove-data-size').forEach(button => {
        button.addEventListener('click', function() {
            dataSizeToRemove = this.dataset.id;
            removeSizeModal.show();
        });
    });

    document.getElementById('confirmRemoveSizeBtn').addEventListener('click', function() {
        if (dataSizeToRemove) {
            // Redirect or submit a form to handle the removal
            // Ensure the product ID is included in the redirect URL
            window.location.href = `edit_product.php?id=<?php echo $product['id']; ?>&remove_size_id=${dataSizeToRemove}`;
            removeSizeModal.hide();
        }
    });
});
</script> 