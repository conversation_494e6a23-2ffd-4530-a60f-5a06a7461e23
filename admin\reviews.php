<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Determine the base path of the application
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}
$reviews = $conn->query('SELECT r.*, u.full_name, p.name AS product_name FROM reviews r JOIN users u ON r.user_id = u.id JOIN products p ON r.product_id = p.id ORDER BY r.created_at DESC');
include '../includes/header.php';
?>
<div class="container py-5">
  <h2 class="mb-4">Manage Reviews</h2>
  <div class="table-responsive">
    <table class="table align-middle">
      <thead>
        <tr>
          <th>ID</th>
          <th>User</th>
          <th>Product</th>
          <th>Rating</th>
          <th>Review</th>
          <th>Date</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
      <?php while($r = $reviews->fetch_assoc()): ?>
        <tr>
          <td><?php echo $r['id']; ?></td>
          <td><?php echo htmlspecialchars($r['full_name']); ?></td>
          <td><?php echo htmlspecialchars($r['product_name']); ?></td>
          <td>
            <span class="text-warning">
              <?php for($i=1;$i<=5;$i++): ?><i class="bi bi-star<?php echo $i <= $r['rating'] ? '-fill' : ''; ?>"></i><?php endfor; ?>
            </span>
          </td>
          <td><?php echo nl2br(htmlspecialchars($r['review'])); ?></td>
          <td><?php echo date('Y-m-d', strtotime($r['created_at'])); ?></td>
          <td><a href="delete_review.php?id=<?php echo $r['id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Delete review?');">Delete</a></td>
        </tr>
      <?php endwhile; ?>
      </tbody>
    </table>
  </div>
  <?php if (isset($base_path)): ?>
  <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary mt-3">Back to Dashboard</a>
  <?php else: ?>
  <a href="dashboard.php" class="btn btn-secondary mt-3">Back to Dashboard</a>
  <?php endif; ?>
</div>
<?php include '../includes/footer.php'; ?> 