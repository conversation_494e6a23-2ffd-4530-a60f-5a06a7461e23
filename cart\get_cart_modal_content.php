<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

if (!isset($_SESSION['user'])) {
    // Return an empty response or an error if the user is not logged in
    // This is basic handling; you might want more sophisticated error reporting
    http_response_code(401); // Unauthorized
    exit;
}

header('Content-Type: text/html');

$cart = $_SESSION['cart'] ?? [];
$total = 0;

if (empty($cart)) {
    echo '<div class="alert alert-info text-center">Your cart is empty.</div>';
    // Also update the total display element
    echo '<script>document.getElementById(\'cart-modal-total\').innerText = \'&#8373;0.00\';</script>';
    exit;
}

$html = '';
$html .= '<div class="table-responsive"><table class="table align-middle"><thead><tr><th>Product</th><th>Data Size</th><th>Quantity</th><th>Price</th><th></th></tr></thead><tbody>';

foreach($cart as $key => $item) {
    // Fetch product name (since it's not stored in the cart session item)
    $stmt = $conn->prepare('SELECT name FROM products WHERE id = ?');
    $stmt->bind_param('i', $item['product_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $product = $result->fetch_assoc();
    $product_name = htmlspecialchars($product['name'] ?? 'Unknown Product');
    $stmt->close();

    // Use the price and size stored in the cart item
    $price = $item['price'] ?? 0;
    $data_size_display = htmlspecialchars($item['size'] ?? 'N/A');
    $beneficiary_number = htmlspecialchars($item['beneficiary_number'] ?? 'N/A');

    $line_total = $price * $item['quantity'];
    $total += $line_total;

    $html .= '<tr>';
    $html .= '<td>' . $product_name . '</td>';
    $html .= '<td>';
    $html .= '<div class="d-flex flex-column">';
    $html .= '<span class="text-success">' . $data_size_display . ' - &#8373;' . number_format($price, 2) . '</span>';
    $html .= '<span class="text-muted">x' . $item['quantity'] . '</span>';
     if (!empty($item['beneficiary_number'])) {
         $html .= '<small class="text-muted">Beneficiary: ' . $beneficiary_number . '</small>';
     }
    $html .= '</div>';
    $html .= '</td>';
    $html .= '<td>&#8373;' . number_format($line_total, 2) . '</td>';
    // Add a remove button - this will require a separate AJAX call or form
    $html .= '<td><button type="button" class="btn btn-sm btn-danger remove-from-modal" data-cart-key="' . htmlspecialchars($key) . '">Remove</button></td>';
    $html .= '</tr>';
}

$html .= '</tbody></table></div>';

// Update the total in the modal footer
$html .= '<script>document.getElementById(\'cart-modal-total\').innerText = \'&#8373;' . number_format($total, 2) . '\';</script>';

echo $html;

$conn->close();
?> 