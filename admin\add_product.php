<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Determine the base path of the application
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$success = $error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $network = $_POST['network'];
    $description = $_POST['description'];
    $active = isset($_POST['active']) ? 1 : 0;
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Insert product
        $stmt = $conn->prepare('INSERT INTO products (name, network, description, active) VALUES (?, ?, ?, ?)');
        $stmt->bind_param('sssi', $name, $network, $description, $active);
        $stmt->execute();
        $product_id = $conn->insert_id;
        
        // Insert data sizes
        $sizes = $_POST['sizes'];
        $prices = $_POST['prices'];
        $size_active = $_POST['size_active'] ?? [];
        
        $stmt = $conn->prepare('INSERT INTO data_sizes (product_id, size, price, active) VALUES (?, ?, ?, ?)');
        
        for($i = 0; $i < count($sizes); $i++) {
            if(!empty($sizes[$i]) && !empty($prices[$i])) {
                $is_active = isset($size_active[$i]) ? 1 : 0;
                $stmt->bind_param('isdi', $product_id, $sizes[$i], $prices[$i], $is_active);
                $stmt->execute();
            }
        }
        
        $conn->commit();
        $success = 'Product and data sizes added successfully!';
        
    } catch (Exception $e) {
        $conn->rollback();
        $error = 'Error adding product: ' . $e->getMessage();
    }
}

include '../includes/header.php';
?>

<div class="container py-5">
    <h2 class="mb-4">Add New Product</h2>

    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <form method="post" id="productForm">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Product Information</h5>
                <div class="mb-3">
                    <label for="name" class="form-label">Product Name</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="network" class="form-label">Network</label>
                    <select class="form-select" id="network" name="network" required>
                        <option value="">Select Network</option>
                        <option value="MTN">MTN</option>
                        <option value="Vodafone">Vodafone</option>
                        <option value="AirtelTigo">AirtelTigo</option>
                        <option value="MTN AFA">MTN AFA</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
                <div class="form-check mb-3">
                    <input type="checkbox" class="form-check-input" id="active" name="active" checked>
                    <label class="form-check-label" for="active">Product Active</label>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Data Sizes and Prices</h5>
                <div id="dataSizes">
                    <div class="row mb-3 data-size-row">
                        <div class="col-md-4">
                            <label class="form-label">Data Size</label>
                            <input type="text" class="form-control" name="sizes[]" placeholder="e.g., 1GB">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Price (GHS)</label>
                            <input type="number" step="0.01" class="form-control" name="prices[]">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Active</label>
                            <div class="form-check mt-2">
                                <input type="checkbox" class="form-check-input" name="size_active[]" checked>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-danger btn-sm remove-size" style="display: none;">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-secondary" id="addSize">
                    <i class="bi bi-plus-circle me-2"></i>Add Another Size
                </button>
            </div>
        </div>

        <button type="submit" class="btn btn-primary">Add Product</button>
        <a href="<?php echo $base_path; ?>admin/products.php" class="btn btn-secondary">Cancel</a>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dataSizes = document.getElementById('dataSizes');
    const addSizeBtn = document.getElementById('addSize');
    
    // Show remove button if more than one row
    function updateRemoveButtons() {
        const rows = dataSizes.getElementsByClassName('data-size-row');
        const removeButtons = dataSizes.getElementsByClassName('remove-size');
        for(let i = 0; i < removeButtons.length; i++) {
            removeButtons[i].style.display = rows.length > 1 ? 'block' : 'none';
        }
    }
    
    // Add new size row
    addSizeBtn.addEventListener('click', function() {
        const row = dataSizes.querySelector('.data-size-row').cloneNode(true);
        // Clear input values
        row.querySelectorAll('input').forEach(input => {
            if(input.type === 'checkbox') {
                input.checked = true;
            } else {
                input.value = '';
            }
        });
        dataSizes.appendChild(row);
        updateRemoveButtons();
    });
    
    // Remove size row
    dataSizes.addEventListener('click', function(e) {
        if(e.target.closest('.remove-size')) {
            e.target.closest('.data-size-row').remove();
            updateRemoveButtons();
        }
    });
    
    // Initial update of remove buttons
    updateRemoveButtons();
});
</script>

<?php include '../includes/footer.php'; ?> 