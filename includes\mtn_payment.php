<?php
class MTNPayment {
    private $client_id;
    private $client_secret;
    private $subscription_key;
    private $environment; // 'sandbox' or 'production'
    private $base_url;
    
    public function __construct($client_id, $client_secret, $subscription_key, $environment = 'sandbox') {
        $this->client_id = $client_id;
        $this->client_secret = $client_secret;
        $this->subscription_key = $subscription_key;
        $this->environment = $environment;
        $this->base_url = $environment === 'sandbox' 
            ? 'https://sandbox.momodeveloper.mtn.com'
            : 'https://api.mtn.com';
    }
    
    public function requestToPay($amount, $currency, $external_id, $payer_message, $payee_note, $callback_url) {
        // Generate UUID for the request
        $uuid = uniqid('ELS_', true);
        
        // Prepare the request payload
        $payload = [
            'amount' => $amount,
            'currency' => $currency,
            'externalId' => $external_id,
            'payer' => [
                'partyIdType' => 'MSISDN',
                'partyId' => '' // This will be collected from the user
            ],
            'payerMessage' => $payer_message,
            'payeeNote' => $payee_note
        ];
        
        // Get access token
        $token = $this->getAccessToken();
        if (!$token) {
            return ['success' => false, 'error' => 'Failed to get access token'];
        }
        
        // Make the API request
        $ch = curl_init($this->base_url . '/collection/v1_0/requesttopay');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'X-Reference-Id: ' . $uuid,
            'X-Target-Environment: ' . $this->environment,
            'Ocp-Apim-Subscription-Key: ' . $this->subscription_key,
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 202) {
            return [
                'success' => true,
                'transaction_id' => $uuid,
                'status' => 'PENDING'
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Payment request failed',
            'details' => json_decode($response, true)
        ];
    }
    
    private function getAccessToken() {
        $credentials = base64_encode($this->client_id . ':' . $this->client_secret);
        
        $ch = curl_init($this->base_url . '/collection/token/');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Basic ' . $credentials,
            'Ocp-Apim-Subscription-Key: ' . $this->subscription_key
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            $data = json_decode($response, true);
            return $data['access_token'] ?? null;
        }
        
        return null;
    }
    
    public function getTransactionStatus($transaction_id) {
        $token = $this->getAccessToken();
        if (!$token) {
            return ['success' => false, 'error' => 'Failed to get access token'];
        }
        
        $ch = curl_init($this->base_url . '/collection/v1_0/requesttopay/' . $transaction_id);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token,
            'X-Target-Environment: ' . $this->environment,
            'Ocp-Apim-Subscription-Key: ' . $this->subscription_key
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 200) {
            $data = json_decode($response, true);
            return [
                'success' => true,
                'status' => $data['status'] ?? 'UNKNOWN'
            ];
        }
        
        return [
            'success' => false,
            'error' => 'Failed to get transaction status',
            'details' => json_decode($response, true)
        ];
    }
}

// Function to get MTN payment button
function get_mtn_payment_button($amount, $order_id, $user_email, $base_path) {
    // MTN API credentials - Replace these with your actual credentials
    $mtn_client_id = 'YOUR_CLIENT_ID';
    $mtn_client_secret = 'YOUR_CLIENT_SECRET';
    $mtn_subscription_key = 'YOUR_SUBSCRIPTION_KEY';
    $mtn_environment = 'sandbox'; // Change to 'production' for live environment
    
    $mtn = new MTNPayment($mtn_client_id, $mtn_client_secret, $mtn_subscription_key, $mtn_environment);
    
    return <<<HTML
<div class="mtn-payment-form">
    <div class="mb-3">
        <label for="mtn_number" class="form-label">MTN Mobile Money Number</label>
        <input type="tel" class="form-control" id="mtn_number" name="mtn_number" 
               pattern="^(233|0)[0-9]{9}$" 
               placeholder="e.g., 233XXXXXXXXX or 0XXXXXXXXX" required>
        <small class="form-text text-muted">Enter your MTN Mobile Money number</small>
    </div>
    <button type="button" class="btn btn-warning w-100" onclick="initiateMTNPayment()">
        <i class="bi bi-phone"></i> Pay with MTN Mobile Money
    </button>
</div>

<script>
function initiateMTNPayment() {
    const mtnNumber = document.getElementById('mtn_number').value;
    if (!mtnNumber.match(/^(233|0)[0-9]{9}$/)) {
        alert('Please enter a valid MTN Mobile Money number');
        return;
    }
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
    
    // Make AJAX call to initiate payment
    fetch('{$base_path}cart/initiate_mtn_payment.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            amount: {$amount},
            order_id: '{$order_id}',
            mtn_number: mtnNumber
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message and redirect to status check
            alert('Payment initiated! Please check your phone for the MTN Mobile Money prompt.');
            window.location.href = '{$base_path}cart/payment_status.php?order_id={$order_id}&transaction_id=' + data.transaction_id;
        } else {
            alert('Payment initiation failed: ' + (data.error || 'Unknown error'));
            button.disabled = false;
            button.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while initiating payment');
        button.disabled = false;
        button.innerHTML = originalText;
    });
}
</script>
HTML;
}
?> 