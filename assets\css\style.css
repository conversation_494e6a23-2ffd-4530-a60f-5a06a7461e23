/* ElsBee Data Custom Styles */

:root {
  --primary: #6a6ee7;
  --primary-dark: #4b4fcf;
  --accent: #5f5be3;
  --gradient-start: #6a6ee7;
  --gradient-end: #5f5be3;
  --secondary: #f8f9fa;
}

body {
  background-color: var(--secondary);
  /* padding-top: 76px; /* Removed to prevent conflict with fixed preloader */
}

.content {
    padding-top: 80px; /* Add padding to content to make space for fixed navbar */
}

.bg-primary,
.btn-primary {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  border: none;
}

.btn-primary,
.btn-outline-primary {
  color: #fff;
  font-weight: 500;
  border-radius: 8px;
}

.btn-outline-primary {
  border: 2px solid var(--primary);
  color: var(--primary);
  background: #fff;
}

.btn-outline-primary:hover {
  background: var(--primary);
  color: #fff;
}

.navbar{
    background: #6a6ee7 !important;
    min-height: 76px;
}

.footer, .bg-dark {
  background: #23242a !important;
}

.navbar .navbar-brand, .navbar .nav-link, .footer, .footer a {
  color: #fff !important;
}

section.bg-primary {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
}

.card {
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(90, 100, 255, 0.07);
}


.data-service-card .card-img-top {
    max-height: 50px; /* Adjust this value to your desired image height */
    object-fit: contain; /* Ensures the image is scaled nicely within the box */
}

.circle {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--primary);
  color: #fff;
  font-size: 1.5rem;
  margin: 0 auto;
}

.star-rating .bi-star,
.star-rating .bi-star-fill {
  color: #f7c948;
  font-size: 1.2rem;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--primary-dark);
  font-weight: 700;
}

footer {
  background: #23242a;
  color: #fff;
}

footer a {
  color: #bdbdbd;
}

footer a:hover {
  color: #fff;
}

/* Responsive tweaks */
@media (max-width: 767px) {
  .card {
    margin-bottom: 1.5rem;
  }
}

/* Button and Card Hover Effects */
.btn-primary, .btn-outline-primary, .btn-secondary {
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.btn-primary:hover, .btn-primary:focus {
  background: linear-gradient(135deg, var(--primary-dark), var(--accent));
  color: #fff;
  box-shadow: 0 4px 16px rgba(90, 100, 255, 0.15);
}
.btn-outline-primary:focus, .btn-outline-primary:active {
  box-shadow: 0 4px 16px rgba(90, 100, 255, 0.10);
}
.card {
  transition: box-shadow 0.2s, transform 0.2s;
}
.card:hover {
  box-shadow: 0 8px 32px rgba(90, 100, 255, 0.13);
  transform: translateY(-2px) scale(1.01);
}
.form-control:focus, .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(106, 110, 231, 0.15);
}

/* Hero Section */
.hero-section {
    margin-top: -20px;
  background: #6a6ee7 !important;
  background-size: 400% 400%;
  animation: gradientBG 15s ease infinite;
  padding: 120px 0 100px 0;
  min-height: 650px;
  display: flex;
  align-items: center;
}
.hero-section h1,
.hero-section p {
  color: #fff;
}
.hero-section .btn,
.btn-buy-now {
  background: #fff;
  color: var(--primary-dark);
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.2rem;
  padding: 12px 32px;
  box-shadow: 0 2px 12px rgba(90, 100, 255, 0.07);
  border: none;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.hero-section .btn:hover,
.btn-buy-now:hover {
  background: var(--primary-dark);
  color: #fff;
  box-shadow: 0 4px 16px rgba(90, 100, 255, 0.15);
}

/* Buy Now Button for cards */
.btn-buy-now, .card .btn {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: #fff;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  padding: 10px 24px;
  border: none;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.btn-buy-now:hover, .card .btn:hover {
  background: var(--primary-dark);
  color: #fff;
  box-shadow: 0 4px 16px rgba(90, 100, 255, 0.15);
}

/* Login & Register Page Styles */
.login-register-bg {
  background: linear-gradient(135deg, 
    #6a6ee7 0%,
    #5f5be3 25%,
    #4b4fcf 50%,
    #5f5be3 75%,
    #6a6ee7 100%
  );
  background-size: 400% 400%;
  animation: gradientBG 15s ease infinite;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
  position: relative;
  overflow: hidden;
}

.login-register-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, 
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  pointer-events: none;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              0 0 0 1px rgba(255, 255, 255, 0.1);
  padding: 2.5rem !important;
  max-width: 420px;
  width: 100%;
  position: relative;
  z-index: 1;
}

.auth-card h3 {
  color: var(--primary-dark);
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  text-align: center;
  font-weight: 700;
}

.auth-card .form-control {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 0.8rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.auth-card .form-control:focus {
  background: #fff;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(106, 110, 231, 0.15);
}

.auth-card label {
  color: #495057;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.auth-card .btn {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: #fff;
  font-weight: 600;
  padding: 0.8rem;
  border-radius: 12px;
  font-size: 1.1rem;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.auth-card .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(106, 110, 231, 0.3);
}

.auth-card .alert {
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.auth-card .auth-links {
  text-align: center;
  margin-top: 1.5rem;
  color: #6c757d;
}

.auth-card .auth-links a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.auth-card .auth-links a:hover {
  color: var(--primary-dark);
}

.auth-card .form-floating {
  margin-bottom: 1rem;
}

.auth-card .form-floating label {
  padding: 1rem;
}

.auth-card .form-floating > .form-control {
  height: calc(3.5rem + 2px);
  padding: 1rem;
}

/* Button Border Radius Update */
.btn, .btn-primary, .btn-outline-primary, .btn-buy-now, .card .btn {
  border-radius: 12px;
}

/* Navbar Styles */
.navbar {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, 
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  pointer-events: none;
  z-index: -1;
}

.navbar .navbar-brand,
.navbar .nav-link {
  color: #fff !important;
  position: relative;
  z-index: 1;
}

.navbar .nav-link:hover {
  color: rgba(255, 255, 255, 0.9) !important;
}

.navbar .dropdown-menu {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.navbar .dropdown-item {
  color: var(--primary-dark);
  padding: 0.7rem 1.5rem;
  transition: all 0.3s ease;
}

.navbar .dropdown-item:hover {
  background: rgba(106, 110, 231, 0.1);
  color: var(--primary);
}

.navbar .dropdown-item i {
  width: 20px;
  margin-right: 8px;
} 