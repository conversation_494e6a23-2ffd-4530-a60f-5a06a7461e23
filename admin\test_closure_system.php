<?php
session_start();

// Determine the base path of the application
$base_path = '/';

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

// Test the closure system functions
$test_results = [];

// Test 1: Check if time_manager functions exist
$test_results['functions'] = [
    'get_ghana_time' => function_exists('get_ghana_time'),
    'is_website_closed' => function_exists('is_website_closed'),
    'get_admin_closure_override' => function_exists('get_admin_closure_override'),
    'set_admin_closure_override' => function_exists('set_admin_closure_override'),
    'get_closure_status_info' => function_exists('get_closure_status_info')
];

// Test 2: Check database tables
$test_results['tables'] = [];
try {
    $result = $conn->query("SHOW TABLES LIKE 'settings'");
    $test_results['tables']['settings'] = $result->num_rows > 0;
    
    $result = $conn->query("SHOW TABLES LIKE 'activity_logs'");
    $test_results['tables']['activity_logs'] = $result->num_rows > 0;
} catch (Exception $e) {
    $test_results['tables']['error'] = $e->getMessage();
}

// Test 3: Try to get current status (if functions exist)
if (function_exists('get_closure_status_info')) {
    try {
        require '../includes/time_manager.php';
        $test_results['status'] = get_closure_status_info();
        $test_results['admin_override'] = get_admin_closure_override();
        $test_results['ghana_time'] = get_ghana_time_display();
    } catch (Exception $e) {
        $test_results['status_error'] = $e->getMessage();
    }
}

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0"><i class="bi bi-bug"></i> Closure System Diagnostic Test</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> This page tests all components of the admin override system to identify any issues.
                    </div>

                    <!-- Function Tests -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-code-square"></i> Function Availability Test</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($test_results['functions'] as $func => $exists): ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-<?php echo $exists ? 'check-circle text-success' : 'x-circle text-danger'; ?> me-2"></i>
                                            <code><?php echo $func; ?>()</code>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Database Tests -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-database"></i> Database Table Test</h6>
                        </div>
                        <div class="card-body">
                            <?php if (isset($test_results['tables']['error'])): ?>
                                <div class="alert alert-danger">
                                    <i class="bi bi-exclamation-triangle"></i> Database Error: <?php echo $test_results['tables']['error']; ?>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-<?php echo $test_results['tables']['settings'] ? 'check-circle text-success' : 'x-circle text-danger'; ?> me-2"></i>
                                            <span>Settings Table</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-<?php echo $test_results['tables']['activity_logs'] ? 'check-circle text-success' : 'x-circle text-danger'; ?> me-2"></i>
                                            <span>Activity Logs Table</span>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Status Tests -->
                    <?php if (isset($test_results['status'])): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-clock"></i> Current System Status</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Website Status:</strong> 
                                            <span class="badge <?php echo $test_results['status']['is_closed'] ? 'bg-danger' : 'bg-success'; ?>">
                                                <?php echo $test_results['status']['is_closed'] ? 'CLOSED' : 'OPEN'; ?>
                                            </span>
                                        </p>
                                        <p><strong>Reason:</strong> <?php echo $test_results['status']['reason']; ?></p>
                                        <p><strong>Message:</strong> <?php echo $test_results['status']['message']; ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Admin Override:</strong> 
                                            <?php 
                                            if ($test_results['admin_override'] === null) {
                                                echo '<span class="badge bg-secondary">Auto Mode</span>';
                                            } elseif ($test_results['admin_override'] === true) {
                                                echo '<span class="badge bg-danger">Manually Closed</span>';
                                            } else {
                                                echo '<span class="badge bg-success">Manually Opened</span>';
                                            }
                                            ?>
                                        </p>
                                        <p><strong>Ghana Time:</strong> <?php echo $test_results['ghana_time']; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php elseif (isset($test_results['status_error'])): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Status Test Error</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-danger">
                                    <strong>Error:</strong> <?php echo $test_results['status_error']; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Overall Status -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-clipboard-check"></i> Overall System Status</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $all_functions_exist = array_reduce($test_results['functions'], function($carry, $item) {
                                return $carry && $item;
                            }, true);
                            
                            $tables_exist = isset($test_results['tables']['settings']) && 
                                          isset($test_results['tables']['activity_logs']) &&
                                          $test_results['tables']['settings'] && 
                                          $test_results['tables']['activity_logs'];
                            
                            $status_working = isset($test_results['status']) && !isset($test_results['status_error']);
                            ?>
                            
                            <?php if ($all_functions_exist && $tables_exist && $status_working): ?>
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle"></i> <strong>All systems operational!</strong> 
                                    The admin override system is working correctly.
                                </div>
                                <div class="d-grid gap-2 d-md-flex">
                                    <a href="<?php echo $base_path; ?>admin/website_closure.php" class="btn btn-primary">
                                        <i class="bi bi-gear"></i> Manage Website Closure
                                    </a>
                                    <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary">
                                        <i class="bi bi-speedometer2"></i> Admin Dashboard
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i> <strong>System issues detected!</strong> 
                                    Some components need attention.
                                </div>
                                <div class="d-grid gap-2 d-md-flex">
                                    <?php if (!$tables_exist): ?>
                                        <a href="<?php echo $base_path; ?>admin/setup_closure_system.php" class="btn btn-warning">
                                            <i class="bi bi-tools"></i> Run Setup
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary">
                                        <i class="bi bi-speedometer2"></i> Admin Dashboard
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
