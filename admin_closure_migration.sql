-- Database migration for Admin Closure Management System
-- Run this SQL to add the necessary table for activity logging

-- Create activity_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_action ON activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at);

-- Insert initial settings if they don't exist
INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES 
('admin_closure_override', NULL, 'Admin manual override for website closure'),
('closure_schedule_enabled', '1', 'Enable automatic closure schedule'),
('closure_start_time', '21:00', 'Time when website closes (24-hour format)'),
('closure_end_time', '07:00', 'Time when website opens (24-hour format)');

-- Sample activity log entry (remove this line after running)
-- INSERT INTO activity_logs (user_id, action, description) VALUES (1, 'system_setup', 'Admin closure management system initialized');
