<?php
session_start();

// Determine the base path of the application
$base_path = '/';

require '../includes/db.php';
require '../includes/auth.php';
require '../includes/time_manager.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$success = $error = '';

// Handle POST request to change closure status
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $admin_id = $_SESSION['user']['id'];
    
    if (set_admin_closure_override($action, $admin_id)) {
        switch ($action) {
            case 'closed':
                $success = 'Website has been manually closed. All visitors will now see the closed page.';
                break;
            case 'open':
                $success = 'Website has been manually opened. All visitors can now access the site.';
                break;
            case 'auto':
                $success = 'Website closure returned to automatic mode. Schedule: Closed 9 PM - 7 AM Ghana time.';
                break;
        }
    } else {
        $error = 'Failed to update website closure status. Please try again.';
    }
}

// Get current status
$status_info = get_closure_status_info();
$admin_override = get_admin_closure_override();
$ghana_time = get_ghana_time();
$next_opening = get_next_opening_time();
$next_closing = get_next_closing_time();

include '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-gear"></i> Website Closure Management</h2>
                <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                </a>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Current Status Card -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-info-circle"></i> Current Website Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="status-indicator <?php echo $status_info['is_closed'] ? 'status-closed' : 'status-open'; ?> me-3"></div>
                                        <div>
                                            <h4 class="mb-1 <?php echo $status_info['is_closed'] ? 'text-danger' : 'text-success'; ?>">
                                                <?php echo $status_info['is_closed'] ? 'CLOSED' : 'OPEN'; ?>
                                            </h4>
                                            <p class="mb-0 text-muted"><?php echo $status_info['message']; ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="text-md-end">
                                        <p class="mb-1"><strong>Current Ghana Time:</strong></p>
                                        <p class="mb-0 text-primary" id="current-time"><?php echo get_ghana_time_display(); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-toggles"></i> Manual Override Controls</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-4">
                                Use these controls to manually override the automatic closure schedule. 
                                The website normally closes at 9 PM and opens at 7 AM Ghana time.
                            </p>
                            
                            <form method="post" class="d-inline">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <button type="submit" name="action" value="closed" 
                                                class="btn btn-danger w-100 <?php echo ($admin_override === true) ? 'active' : ''; ?>"
                                                <?php echo ($status_info['is_closed'] && $admin_override === true) ? 'disabled' : ''; ?>>
                                            <i class="bi bi-x-circle"></i> Force Close
                                        </button>
                                        <small class="text-muted d-block mt-1">Close website immediately</small>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" name="action" value="open" 
                                                class="btn btn-success w-100 <?php echo ($admin_override === false) ? 'active' : ''; ?>"
                                                <?php echo (!$status_info['is_closed'] && $admin_override === false) ? 'disabled' : ''; ?>>
                                            <i class="bi bi-check-circle"></i> Force Open
                                        </button>
                                        <small class="text-muted d-block mt-1">Open website immediately</small>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" name="action" value="auto" 
                                                class="btn btn-primary w-100 <?php echo ($admin_override === null) ? 'active' : ''; ?>"
                                                <?php echo ($admin_override === null) ? 'disabled' : ''; ?>>
                                            <i class="bi bi-clock"></i> Auto Mode
                                        </button>
                                        <small class="text-muted d-block mt-1">Follow schedule (9 PM - 7 AM)</small>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule Information -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-calendar-event"></i> Automatic Schedule</h6>
                        </div>
                        <div class="card-body">
                            <div class="schedule-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-moon text-primary me-2"></i>
                                    <div>
                                        <strong>Closes:</strong> 9:00 PM Ghana Time<br>
                                        <small class="text-muted">Next: <?php echo $next_closing->format('l, F j \a\t g:i A'); ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="schedule-item">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-sunrise text-warning me-2"></i>
                                    <div>
                                        <strong>Opens:</strong> 7:00 AM Ghana Time<br>
                                        <small class="text-muted">Next: <?php echo $next_opening->format('l, F j \a\t g:i A'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-shield-check"></i> Admin Access</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info mb-0">
                                <i class="bi bi-info-circle"></i>
                                <strong>Note:</strong> As an admin, you can always access the website even when it's closed. 
                                Regular visitors will see the closed page during closure hours.
                            </div>
                            <div class="mt-3">
                                <a href="<?php echo $base_path; ?>closed.php" class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="bi bi-eye"></i> Preview Closed Page
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
}

.status-open {
    background-color: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.status-closed {
    background-color: #dc3545;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

.schedule-item {
    padding: 0.75rem;
    background: rgba(106, 110, 231, 0.05);
    border-radius: 8px;
}
</style>

<script>
// Update current time every minute
function updateCurrentTime() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
        timeZone: 'Africa/Accra'
    };
    document.getElementById('current-time').textContent = now.toLocaleDateString('en-US', options);
}

// Update time every minute
setInterval(updateCurrentTime, 60000);

// Confirmation for actions
document.querySelectorAll('button[name="action"]').forEach(button => {
    button.addEventListener('click', function(e) {
        const action = this.value;
        let message = '';
        
        switch(action) {
            case 'closed':
                message = 'Are you sure you want to manually close the website? All visitors will be redirected to the closed page.';
                break;
            case 'open':
                message = 'Are you sure you want to manually open the website? All visitors will have access to the site.';
                break;
            case 'auto':
                message = 'Are you sure you want to return to automatic mode? The website will follow the normal schedule (9 PM - 7 AM closure).';
                break;
        }
        
        if (!confirm(message)) {
            e.preventDefault();
        }
    });
});
</script>

<?php include '../includes/footer.php'; ?>
