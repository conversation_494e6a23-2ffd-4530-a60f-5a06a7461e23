<?php
session_start();
require 'includes/db.php';

$error = '';
$success = '';
$token = $_GET['token'] ?? '';

$user = null;

// Validate the token and check expiration
if (!empty($token)) {
    $stmt = $conn->prepare('SELECT id, email, reset_expires_at FROM users WHERE reset_token = ?');
    $stmt->bind_param('s', $token);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();

    if ($user) {
        $current_time = date('Y-m-d H:i:s');
        if ($user['reset_expires_at'] < $current_time) {
            // Token has expired
            $error = 'Password reset token has expired. Please request a new one.';
            $user = null; // Invalidate user data
        }
    } else {
        // Token not found
        $error = 'Invalid password reset token.';
    }
}

// Handle password reset form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $user) {
    $new_password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    if (empty($new_password) || empty($confirm_password)) {
        $error = 'Please enter and confirm your new password.';
    } elseif ($new_password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } else {
        // Hash the new password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

        // Update the password and clear the reset token in the database
        $stmt = $conn->prepare('UPDATE users SET password = ?, reset_token = NULL, reset_expires_at = NULL WHERE id = ?');
        $stmt->bind_param('si', $hashed_password, $user['id']);
        
        if ($stmt->execute()) {
            $success = 'Your password has been reset successfully. You can now log in with your new password.';
            $user = null; // Prevent displaying the form after successful reset
        } else {
            $error = 'Failed to reset password. Please try again.';
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reset Password - ElsBee Data</title>
  <link href="assets/css/style.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="login-register-bg">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-6 col-lg-5">
        <div class="auth-card">
          <h3><i class="bi bi-lock-reset me-2"></i>Reset Password</h3>

          <?php if($error): ?>
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
            </div>
          <?php endif; ?>

          <?php if($success): ?>
            <div class="alert alert-success">
              <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
            </div>
            <div class="auth-links">
              <p class="mb-0"><a href="login.php">Back to Login</a></p>
            </div>
          <?php elseif ($user): // Only show form if token is valid and not expired ?>
            <form method="post">
              <div class="mb-3">
                <label for="password" class="form-label">New Password</label>
                <input type="password" name="password" class="form-control" id="password" required>
              </div>
              <div class="mb-3">
                <label for="confirm_password" class="form-label">Confirm New Password</label>
                <input type="password" name="confirm_password" class="form-control" id="confirm_password" required>
              </div>
              <button type="submit" class="btn w-100">
                <i class="bi bi-check-circle me-2"></i>Reset Password
              </button>
            </form>
          <?php else: // Show link back to forgot password if token is invalid or expired ?>
             <div class="auth-links">
              <p class="mb-0"><a href="forgot_password.php">Request Another Reset Link</a></p>
            </div>
          <?php endif; ?>

        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 