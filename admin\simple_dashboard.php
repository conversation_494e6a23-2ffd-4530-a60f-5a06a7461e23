<?php
session_start();

// Determine the base path of the application
$base_path = '/';

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$users = $conn->query('SELECT COUNT(*) as c FROM users')->fetch_assoc()['c'];
$orders = $conn->query('SELECT COUNT(*) as c FROM orders')->fetch_assoc()['c'];
$products = $conn->query('SELECT COUNT(*) as c FROM products')->fetch_assoc()['c'];
$reviews = $conn->query('SELECT COUNT(*) as c FROM reviews')->fetch_assoc()['c'];

include '../includes/header.php';
?>
<div class="container py-5">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">Admin Dashboard</h2>
    <div class="website-status">
      <span class="badge bg-primary fs-6">
        <i class="bi bi-gear"></i> Admin Panel
      </span>
    </div>
  </div>
  
  <div class="row g-4 mb-4">
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $users; ?></h4>
        <div class="text-muted">Users</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $orders; ?></h4>
        <div class="text-muted">Orders</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $products; ?></h4>
        <div class="text-muted">Products</div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center p-3 shadow-sm h-100">
        <h4><?php echo $reviews; ?></h4>
        <div class="text-muted">Reviews</div>
      </div>
    </div>
  </div>
  
  <div class="row g-3">
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/users.php" class="btn btn-primary w-100 shadow-sm">Manage Users</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/products.php" class="btn btn-primary w-100 shadow-sm">Manage Products</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/orders.php" class="btn btn-primary w-100 shadow-sm">Manage Orders</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/reviews.php" class="btn btn-primary w-100 shadow-sm">Manage Reviews</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/website_closure.php" class="btn btn-warning w-100 shadow-sm"><i class="bi bi-clock"></i> Website Closure</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/settings.php" class="btn btn-secondary w-100 shadow-sm">System Settings</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>admin/activity_logs.php" class="btn btn-secondary w-100 shadow-sm">Activity Logs</a></div>
    <div class="col-md-3"><a href="<?php echo $base_path; ?>test_time.php" class="btn btn-info w-100 shadow-sm">Test Time System</a></div>
  </div>
  
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0"><i class="bi bi-info-circle"></i> Admin Information</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <p><strong>Logged in as:</strong> <?php echo htmlspecialchars($_SESSION['user']['full_name']); ?></p>
              <p><strong>Account Type:</strong> <?php echo htmlspecialchars($_SESSION['user']['account_type']); ?></p>
              <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['user']['email']); ?></p>
            </div>
            <div class="col-md-6">
              <p><strong>Current Time:</strong> <span id="current-time"><?php echo date('Y-m-d H:i:s'); ?></span></p>
              <p><strong>Server:</strong> <?php echo $_SERVER['SERVER_NAME']; ?></p>
              <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Update current time every second
function updateCurrentTime() {
    const now = new Date();
    document.getElementById('current-time').textContent = now.toLocaleString();
}

setInterval(updateCurrentTime, 1000);
</script>

<?php include '../includes/footer.php'; ?>
