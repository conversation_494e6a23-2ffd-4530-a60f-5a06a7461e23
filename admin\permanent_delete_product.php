<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();

// Determine the base path of the application
$base_path = '/datahub/';

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// First check if the product exists and is inactive
$check_stmt = $conn->prepare('SELECT active FROM products WHERE id = ?');
$check_stmt->bind_param('i', $id);
$check_stmt->execute();
$result = $check_stmt->get_result();
$product = $result->fetch_assoc();

if (!$product) {
    header('Location: products.php?error=not_found');
    exit;
}

if ($product['active']) {
    // Don't allow deletion of active products
    header('Location: products.php?error=active_product');
    exit;
}

// Check if there are any orders for this product
$order_check = $conn->prepare('SELECT COUNT(*) as order_count FROM order_items WHERE product_id = ?');
$order_check->bind_param('i', $id);
$order_check->execute();
$order_result = $order_check->get_result()->fetch_assoc();

if ($order_result['order_count'] > 0) {
    // Don't allow deletion if there are orders
    header('Location: products.php?error=has_orders');
    exit;
}

// Start transaction
$conn->begin_transaction();

try {
    // Delete all data sizes first (due to foreign key constraint)
    $stmt = $conn->prepare('DELETE FROM data_sizes WHERE product_id = ?');
    $stmt->bind_param('i', $id);
    $stmt->execute();

    // Then delete the product
    $stmt = $conn->prepare('DELETE FROM products WHERE id = ?');
    $stmt->bind_param('i', $id);
    $stmt->execute();

    $conn->commit();
    header('Location: products.php?deleted=1');
} catch (Exception $e) {
    $conn->rollback();
    header('Location: products.php?error=delete_failed');
}
exit; 