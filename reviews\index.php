<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
include '../includes/header.php';

// When someone writes a review
$success = $error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && is_logged_in()) {
    $user_id = $_SESSION['user']['id'];
    $product_id = intval($_POST['product_id']);
    $rating = intval($_POST['rating']);
    $review = trim($_POST['review']);
    if ($rating < 1 || $rating > 5 || !$review) {
        $error = 'Please provide a valid rating and review.';
    } else {
        // Save it to our database
        $stmt = $conn->prepare('INSERT INTO reviews (user_id, product_id, rating, review) VALUES (?, ?, ?, ?)');
        $stmt->bind_param('iiis', $user_id, $product_id, $rating, $review);
        if ($stmt->execute()) {
            $success = 'Review submitted!';
        } else {
            $error = 'Failed to submit review.';
        }
    }
}
// Get all reviews to show
$reviews = $conn->query('SELECT r.*, u.full_name, p.name AS product_name FROM reviews r JOIN users u ON r.user_id = u.id JOIN products p ON r.product_id = p.id ORDER BY r.created_at DESC');
$products = $conn->query('SELECT id, name FROM products WHERE active = 1');
?>
<div class="container py-5">
  <h2 class="mb-4 text-center">Customer Reviews & Testimonials</h2>
  <?php if($success): ?><div class="alert alert-success"><?php echo $success; ?></div><?php endif; ?>
  <?php if($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>
  <?php if(is_logged_in()): ?>
  <form method="post" class="mb-4">
    <div class="row g-2 align-items-end">
      <div class="col-md-4">
        <label>Product</label>
        <select name="product_id" class="form-select" required>
          <option value="">Select Product</option>
          <?php while($p = $products->fetch_assoc()): ?>
            <option value="<?php echo $p['id']; ?>"><?php echo htmlspecialchars($p['name']); ?></option>
          <?php endwhile; ?>
        </select>
      </div>
      <div class="col-md-2">
        <label>Rating</label>
        <select name="rating" class="form-select" required>
          <option value="">Stars</option>
          <?php for($i=1;$i<=5;$i++): ?><option value="<?php echo $i; ?>"><?php echo $i; ?></option><?php endfor; ?>
        </select>
      </div>
      <div class="col-md-4">
        <label>Your Review</label>
        <textarea name="review" class="form-control" rows="1" required></textarea>
      </div>
      <div class="col-md-2">
        <button type="submit" class="btn btn-primary w-100">Submit</button>
      </div>
    </div>
  </form>
  <?php endif; ?>
  <div class="row g-4">
    <?php if($reviews->num_rows == 0): ?>
      <div class="col-12 text-center text-muted">No reviews yet. Be the first to share your experience!</div>
    <?php else: ?>
      <?php while($r = $reviews->fetch_assoc()): ?>
      <div class="col-12 col-md-6">
        <div class="card p-3 h-100">
          <div class="d-flex flex-wrap align-items-center mb-2">
            <div class="d-flex align-items-center me-auto mb-2 mb-md-0">
              <div class="fw-bold me-2"><?php echo htmlspecialchars($r['full_name']); ?></div>
              <span class="badge me-2"><?php echo htmlspecialchars($r['product_name']); ?></span>
            </div>
            <span class="text-warning me-3">
              <?php for($i=1;$i<=5;$i++): ?>
                <i class="bi bi-star<?php echo $i <= $r['rating'] ? '-fill' : ''; ?>"></i>
              <?php endfor; ?>
            </span>
            <?php if(is_logged_in() && $_SESSION['user']['id'] == $r['user_id']): // Only show buttons to the review author ?>
              <div class="d-flex"> <!-- Group action buttons -->
                <!-- Edit Button -->
                <button type="button" class="btn btn-info btn-sm me-2 edit-review-button" data-bs-toggle="modal" data-bs-target="#editReviewModal" data-review-id="<?php echo $r['id']; ?>" data-review-rating="<?php echo $r['rating']; ?>" data-review-text="<?php echo htmlspecialchars($r['review'], ENT_QUOTES); ?>">Edit</button>
                
                <!-- Delete Button Form -->
                <form action="delete_review.php" method="post" class="delete-review-form">
                  <input type="hidden" name="review_id" value="<?php echo $r['id']; ?>">
                  <button type="submit" class="btn btn-danger btn-sm delete-review-button" data-bs-toggle="modal" data-bs-target="#confirmDeleteModal">Delete</button>
                </form>
              </div>
            <?php endif; ?>
          </div>
          <div><?php echo nl2br(htmlspecialchars($r['review'])); ?></div>
          <div class="text-end small text-muted mt-2"><?php echo date('Y-m-d', strtotime($r['created_at'])); ?></div>
        </div>
      </div>
      <?php endwhile; ?>
    <?php endif; ?>
  </div>
</div>
<?php include '../includes/footer.php'; ?>

<!-- Custom Confirmation Modal (Delete) -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-sm">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="confirmDeleteModalLabel">Confirm Delete</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete this review?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteButton">Delete</button>
      </div>
    </div>
  </div>
</div>

<!-- Custom Edit Review Modal -->
<div class="modal fade" id="editReviewModal" tabindex="-1" aria-labelledby="editReviewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editReviewModalLabel">Edit Your Review</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="editReviewForm" method="post" action="edit_review.php">
        <div class="modal-body">
            <input type="hidden" name="review_id" id="editReviewId">
            <div class="mb-3">
                <label for="editReviewRating" class="form-label">Rating</label>
                <select name="rating" class="form-select" id="editReviewRating" required>
                  <option value="">Select Rating</option>
                  <?php for($i=1;$i<=5;$i++): ?><option value="<?php echo $i; ?>"><?php echo $i; ?> Stars</option><?php endfor; ?>
                </select>
            </div>
            <div class="mb-3">
                <label for="editReviewText" class="form-label">Your Review</label>
                <textarea name="review_text" class="form-control" id="editReviewText" rows="3" required></textarea>
            </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
// Check for any messages in the URL
const urlParams = new URLSearchParams(window.location.search);
const deleteSuccess = urlParams.get('delete_success');
const deleteError = urlParams.get('delete_error');
const editSuccess = urlParams.get('edit_success');
const editError = urlParams.get('edit_error');
const reviewSuccess = urlParams.get('review_success');
const reviewError = urlParams.get('review_error');

if (deleteSuccess) {
    // Yay! Review deleted
    displayAlert('Review deleted successfully!', 'success');
    history.replaceState({}, document.title, window.location.pathname);
} else if (deleteError) {
    // Oops, couldn't delete
    displayAlert(decodeURIComponent(deleteError), 'danger');
    history.replaceState({}, document.title, window.location.pathname);
} else if (editSuccess) {
    // Great! Review updated
    displayAlert('Review updated successfully!', 'success');
    history.replaceState({}, document.title, window.location.pathname);
} else if (editError) {
    // Hmm, couldn't update
    displayAlert(decodeURIComponent(editError), 'danger');
    history.replaceState({}, document.title, window.location.pathname);
} else if (reviewSuccess) {
    // New review posted!
    const reviewSuccessModalElement = document.getElementById('reviewSuccessModal');
    if (reviewSuccessModalElement) {
        const reviewSuccessModal = new bootstrap.Modal(reviewSuccessModalElement);
        reviewSuccessModal.show();
    }
    history.replaceState({}, document.title, window.location.pathname);
} else if (reviewError) {
    // Couldn't post review
    displayAlert(decodeURIComponent(reviewError), 'danger');
    history.replaceState({}, document.title, window.location.pathname);
}

// Set up edit and delete buttons
document.addEventListener('DOMContentLoaded', function() {
    let reviewIdToDelete = null; // Which review to delete?

    const confirmDeleteModalElement = document.getElementById('confirmDeleteModal');
    const confirmDeleteButton = document.getElementById('confirmDeleteButton');
    const editReviewModal = document.getElementById('editReviewModal');
    const editReviewForm = document.getElementById('editReviewForm');
    const editReviewIdInput = document.getElementById('editReviewId');
    const editReviewRatingSelect = document.getElementById('editReviewRating');
    const editReviewTextInput = document.getElementById('editReviewText');

    // Watch for delete button clicks
    document.querySelectorAll('.delete-review-button').forEach(button => {
        button.addEventListener('click', function(event) {
            event.preventDefault();
            const form = this.closest('.delete-review-form');
            if (form) {
                reviewIdToDelete = form.querySelector('input[name="review_id"]').value;
                if (confirmDeleteModalElement) {
                    console.log('Asking to delete review:', reviewIdToDelete);
                    const confirmDeleteModal = new bootstrap.Modal(confirmDeleteModalElement);
                    confirmDeleteModal.show();
                }
            }
        });
    });

    // Handle click on the modal's confirm button (Delete)
    if (confirmDeleteButton) {
        confirmDeleteButton.addEventListener('click', function() {
            console.log('Confirm delete button clicked for review ID:', reviewIdToDelete);
            if (reviewIdToDelete) {
                // Create a temporary form and submit it to delete_review.php
                const tempForm = document.createElement('form');
                tempForm.method = 'POST';
                tempForm.action = 'delete_review.php';

                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'review_id';
                hiddenInput.value = reviewIdToDelete;
                tempForm.appendChild(hiddenInput);

                // Append the form to the body and submit it
                document.body.appendChild(tempForm);
                tempForm.submit();

                // Clean up the temporary form (will happen after the redirect, but good practice)
                // document.body.removeChild(tempForm);
            }

            // Hide the modal after confirming
            const modalInstance = bootstrap.Modal.getInstance(confirmDeleteModalElement);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    }

    // Add event listener for when the delete modal is completely hidden
    if (confirmDeleteModalElement) {
        confirmDeleteModalElement.addEventListener('hidden.bs.modal', function () {
            console.log('Delete confirmation modal is hidden.');
            reviewIdToDelete = null; // Clear the stored review ID when modal is hidden
        });
    }

    // Handle clicks on Edit buttons to populate and show the edit modal
     document.querySelectorAll('.edit-review-button').forEach(button => {
        button.addEventListener('click', function() {
            const reviewId = this.dataset.reviewId;
            const reviewRating = this.dataset.reviewRating;
            const reviewText = this.dataset.reviewText;

            // Populate the modal form fields
            if (editReviewIdInput) editReviewIdInput.value = reviewId;
            if (editReviewRatingSelect) editReviewRatingSelect.value = reviewRating;
            if (editReviewTextInput) editReviewTextInput.value = reviewText;

            // The modal is shown via data-bs-toggle
        });
    });
});

</script> 