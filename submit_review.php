<?php
session_start();
require 'includes/db.php';
require 'includes/auth.php';

// We'll redirect back to where they came from, or to the home page if we can't tell
$redirect_url = $_SERVER['HTTP_REFERER'] ?? 'index.php';

// Make sure they're logged in before they can review
if (!isset($_SESSION['user']['id'])) {
    // Send them back with a friendly message
    header('Location: ' . $redirect_url . '?review_error=' . urlencode('You must be logged in to submit a review.'));
    exit;
}

// Check if they're trying to submit a review
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = $_SESSION['user']['id'];
    $product_id = filter_input(INPUT_POST, 'product_id', FILTER_VALIDATE_INT);
    $rating = filter_input(INPUT_POST, 'rating', FILTER_VALIDATE_INT);
    $review_text = filter_input(INPUT_POST, 'review_text', FILTER_SANITIZE_STRING);

    // Make sure all the data looks good
    if (!$product_id || $rating === false || $rating < 1 || $rating > 5 || empty($review_text)) {
        // Something's wrong with what they sent
        header('Location: ' . $redirect_url . '?review_error=' . urlencode('Invalid input.'));
        exit;
    }

    // Check if they've already reviewed this product
    $check_stmt = $conn->prepare('SELECT COUNT(*) FROM reviews WHERE user_id = ? AND product_id = ?');
    $check_stmt->bind_param('ii', $user_id, $product_id);
    $check_stmt->execute();
    $review_count = $check_stmt->get_result()->fetch_row()[0];

    if ($review_count > 0) {
        // They've already reviewed this product
        header('Location: ' . $redirect_url . '?review_error=' . urlencode('You have already reviewed this product.'));
        exit;
    }

    // Save their review to the database
    $stmt = $conn->prepare('INSERT INTO reviews (product_id, user_id, rating, review_text) VALUES (?, ?, ?, ?)');
    $stmt->bind_param('iiis', $product_id, $user_id, $rating, $review_text);

    if ($stmt->execute()) {
        // Great! Their review was saved successfully
        header('Location: ' . $redirect_url . '?review_success=1&message=' . urlencode('Review submitted successfully!'));
        exit;
    } else {
        // Oops, something went wrong while saving
        error_log('Review submission failed: ' . $conn->error);
        header('Location: ' . $redirect_url . '?review_error=' . urlencode('Error submitting review.'));
        exit;
    }
} else {
    // They didn't use POST to submit the review
    header('Location: ' . $redirect_url . '?review_error=' . urlencode('Invalid request method.'));
    exit;
}
?> 