<?php
session_start();

// Determine the base path of the application
$base_path = '/'; // Adjust this if your application is in a different subdirectory

require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$order_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$order = null;
$order_items = [];
$error = '';
$success = '';

if ($order_id > 0) {
    // Fetch order details
    $stmt = $conn->prepare('SELECT o.*, u.full_name FROM orders o JOIN users u ON o.user_id = u.id WHERE o.id = ?');
    $stmt->bind_param('i', $order_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $order = $result->fetch_assoc();

    if ($order) {
        // Fetch order items
        $stmt = $conn->prepare('SELECT oi.*, p.name as product_name FROM order_items oi JOIN products p ON oi.product_id = p.id WHERE oi.order_id = ?');
        $stmt->bind_param('i', $order_id);
        $stmt->execute();
        $order_items_result = $stmt->get_result();
        while ($item = $order_items_result->fetch_assoc()) {
            $order_items[] = $item;
        }
    }
} else {
    $error = 'Invalid order ID.';
}

// Handle updating payment status
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_payment_status']) && $order) {
    $new_payment_status = $_POST['payment_status'];
    
    // Validate the new status (basic validation)
    $allowed_statuses = ['Awaiting Manual Payment', 'PAID', 'FAILED'];
    if (in_array($new_payment_status, $allowed_statuses)) {
        $stmt = $conn->prepare('UPDATE orders SET payment_status = ? WHERE id = ?');
        $stmt->bind_param('si', $new_payment_status, $order_id);
        if ($stmt->execute()) {
            $success = 'Payment status updated successfully.';
            
            // --- SMS Sending Logic Start ---
            if ($new_payment_status === 'PAID') {
                // Fetch customer's registered phone number from users table
                $stmt = $conn->prepare('SELECT phone_number FROM users WHERE id = ?');
                $stmt->bind_param('i', $order['user_id']);
                $stmt->execute();
                $result = $stmt->get_result();
                $user_data = $result->fetch_assoc();
                
                if (!$user_data || empty($user_data['phone_number'])) {
                    error_log("No registered phone number found for user ID {$order['user_id']} for Order #{$order['id']}");
                    $error .= " Failed to send SMS: Customer has no registered phone number.";
                } else {
                    // Ensure phone number starts with 0
                    $customer_number = trim($user_data['phone_number']);

                    // Format the phone number correctly for the USMS-GH API
                    // The API expects the format without leading zero but with country code
                    if (strpos($customer_number, '+233') === 0) {
                        // Already has +233 prefix, keep as is
                        $customer_number = str_replace('+', '', $customer_number); // Remove + if present
                    } else if (strpos($customer_number, '233') === 0) {
                        // Already has 233 prefix, keep as is
                    } else if (strpos($customer_number, '0') === 0) {
                        // Starts with 0, replace with 233
                        $customer_number = '233' . substr($customer_number, 1);
                    } else {
                        // No prefix, add 233
                        $customer_number = '233' . $customer_number;
                    }

                    // Log the formatted phone number being used for debugging
                    error_log("Attempting to send SMS to formatted number: {$customer_number} for Order #{$order['id']}");

                    $order_id_for_sms = $order['id'];
                    $order_total_for_sms = number_format($order['total'], 2);
                    
                    // Set up our SMS service (USMS-GH)
                    $usmsgh_api_token = '2183|jfU8a7PQDCCYfdPduCELeCUpHM3vxRfAVM0rEfYg49081a84'; // TODO: Move this to a config file later
                    $usmsgh_api_endpoint = 'https://webapp.usmsgh.com/api/sms/send';
                    $sms_sender_id = 'ElsBee Data';
                    
                    // Create a friendly message for the customer
                    $sms_message = "Dear Customer, your payment of GHS {$order_total_for_sms} for Order #{$order_id_for_sms} has been confirmed. Your order is now being processed. Thank you for shopping with ElsBee Data!";
                    
                    // Get ready to send the SMS
                    $ch = curl_init($usmsgh_api_endpoint);
                    
                    // Tell the SMS service who we are
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Authorization: Bearer ' . $usmsgh_api_token,
                        'Accept: application/json',
                        'Content-Type: application/json'
                    ]);
                    
                    // Put together what we want to send
                    $request_body = json_encode([
                        'recipient' => $customer_number,
                        'sender_id' => $sms_sender_id,
                        'type' => 'plain',
                        'message' => $sms_message
                    ]);
                    
                    // Set up how we want to send the SMS
                    curl_setopt_array($ch, [
                        CURLOPT_POST => true,
                        CURLOPT_POSTFIELDS => $request_body,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_SSL_VERIFYPEER => true,
                        CURLOPT_TIMEOUT => 30
                    ]);
                    
                    // Try to send the SMS
                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    $curl_error = curl_error($ch);
                    curl_close($ch);
                    
                    // Keep a record of what happened
                    error_log("SMS sending attempt for Order #{$order_id_for_sms}: HTTP Code: {$http_code}, Response: {$response}");
                    
                    if ($curl_error) {
                        // Something went wrong with the connection
                        error_log("Couldn't connect to SMS service for Order #{$order_id_for_sms}: {$curl_error}");
                        $error .= " Failed to send SMS: Connection error - {$curl_error}";
                    } elseif ($http_code === 401) {
                        // Our API token might be wrong or expired
                        error_log("SMS service didn't recognize our token for Order #{$order_id_for_sms}");
                        $error .= " Failed to send SMS: Our SMS service token needs to be checked.";
                    } elseif ($http_code !== 200) {
                        // Something else went wrong
                        $error .= " Failed to send SMS. Got response code: {$http_code}";
                        if ($response) {
                            $error .= " Response: " . substr($response, 0, 100) . "...";
                        }
                    } else {
                        // Try to parse the JSON response
                        $response_data = json_decode($response, true);
                        if (json_last_error() === JSON_ERROR_NONE) {
                            // Check if the response indicates success
                            if (isset($response_data['status']) && $response_data['status'] === 'success') {
                                // SMS sent successfully
                                error_log("SMS sent successfully for Order #{$order_id_for_sms}");
                            } else {
                                $error .= " SMS API returned error: " . ($response_data['message'] ?? 'Unknown error');
                            }
                        } else {
                            $error .= " Failed to parse SMS API response";
                        }
                    }
                }
            }
            // --- SMS Sending Logic End ---

            // Refresh order data after update
            $stmt = $conn->prepare('SELECT o.*, u.full_name FROM orders o JOIN users u ON o.user_id = u.id WHERE o.id = ?');
            $stmt->bind_param('i', $order_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $order = $result->fetch_assoc();
        } else {
            $error = 'Error updating payment status.';
        }
    } else {
        $error = 'Invalid payment status provided.';
    }
}

// Handle updating order status
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_order_status']) && $order) {
    $new_order_status = $_POST['order_status'];
    
    // Validate the new status
    $allowed_order_statuses = ['Pending', 'Processing', 'Completed', 'Cancelled'];
    if (in_array($new_order_status, $allowed_order_statuses)) {
        $stmt = $conn->prepare('UPDATE orders SET status = ? WHERE id = ?');
        $stmt->bind_param('si', $new_order_status, $order_id);
        if ($stmt->execute()) {
            $success = 'Order status updated successfully.';

            // Require the email function file (moved outside status checks)
            require_once '../includes/email.php';

            // --- Send Order Processing Email ---
            if ($new_order_status === 'Processing') {
                // Fetch user email
                $user_email = '';
                if (isset($order['user_id'])) {
                    $user_stmt = $conn->prepare('SELECT email FROM users WHERE id = ?');
                    $user_stmt->bind_param('i', $order['user_id']);
                    $user_stmt->execute();
                    $user_result = $user_stmt->get_result();
                    $user_data = $user_result->fetch_assoc();
                    if($user_data) {
                        $user_email = $user_data['email'];
                    }
                }

                // Fetch order items to include in email
                $order_items_email = [];
                $order_items_stmt_email = $conn->prepare('SELECT oi.*, p.name as product_name, oi.beneficiary_number FROM order_items oi JOIN products p ON oi.product_id = p.id WHERE oi.order_id = ?');
                $order_items_stmt_email->bind_param('i', $order_id);
                $order_items_stmt_email->execute();
                $order_items_result_email = $order_items_stmt_email->get_result();
                while ($item_email = $order_items_result_email->fetch_assoc()) {
                    $order_items_email[] = $item_email;
                }

                if (!empty($user_email) && !empty($order_items_email)) {
                    $subject = "Your ElsBee Data Order #{$order_id} is now Processing";

                    $message = "<div style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 8px;\">";
                    $message .= "<h2 style=\"color: #0056b3; margin-top: 0;\">Order Status Updated: Processing</h2>";
                    $message .= "<p>Your order (ID: <strong>#{$order_id}</strong>) is now being processed.</p>";

                    $message .= "<h3 style=\"margin-top: 20px; color: #555;\">Order Summary:</h3>";
                    $message .= "<ul style=\"list-style: none; padding: 0; border-top: 1px solid #eee; margin-top: 10px;\">";
                    foreach ($order_items_email as $item_email) {
                        $message .= "<li style=\"padding: 10px 0; border-bottom: 1px solid #eee;\">";
                        $message .= "<strong>" . htmlspecialchars($item_email['product_name']) . " (" . htmlspecialchars($item_email['data_size']) . ") x" . htmlspecialchars($item_email['quantity']) . "</strong> - &#8373;" . number_format($item_email['price'], 2);
                         if (!empty($item_email['beneficiary_number'])) {
                            $message .= "<br><small style=\"color: #777;\">Beneficiary: " . htmlspecialchars($item_email['beneficiary_number']) . "</small>";
                        }
                        $message .= "</li>";
                    }
                    $message .= "</ul>";

                    $message .= "<p style=\"font-size: 1.2em; font-weight: bold; margin-top: 20px; color: #28a745;\">Total: &#8373;" . number_format($order['total'], 2) . "</p>";

                    $message .= "<p style=\"margin-top: 20px;\">We will notify you again once your order has been completed.</p>";
                    $message .= "<p>Thank you for your patience.</p>";

                    // Send the email
                    $from_email = "<EMAIL>";
                    $from_name = "ElsBee Data";
                    $from_header = "{$from_name} <{$from_email}>";

                    $email_sent = send_email($user_email, $subject, $message, $from_header);

                    if (!$email_sent) {
                        error_log("Failed to send order processing email for Order #{$order_id} to {$user_email}");
                        $error .= " Failed to send order processing email.";
                    } else {
                         $success .= " Order processing email sent to {$user_email}.";
                    }
                } else {
                     error_log("Order processing email skipped for Order #{$order_id}. User email or items missing.");
                     $error .= " Could not send order processing email (missing user email or order items).";
                }
            }
             // --- End Send Order Processing Email ---

            // --- Send Order Completion Email ---
            if ($new_order_status === 'Completed') {
                // Email function already required above

                // Fetch user email
                $user_email = '';
                if (isset($order['user_id'])) {
                    $user_stmt = $conn->prepare('SELECT email FROM users WHERE id = ?');
                    $user_stmt->bind_param('i', $order['user_id']);
                    $user_stmt->execute();
                    $user_result = $user_stmt->get_result();
                    $user_data = $user_result->fetch_assoc();
                    if($user_data) {
                        $user_email = $user_data['email'];
                    }
                }

                // Fetch order items again to include in email
                $order_items_email = [];
                $order_items_stmt_email = $conn->prepare('SELECT oi.*, p.name as product_name, oi.beneficiary_number FROM order_items oi JOIN products p ON oi.product_id = p.id WHERE oi.order_id = ?');
                $order_items_stmt_email->bind_param('i', $order_id);
                $order_items_stmt_email->execute();
                $order_items_result_email = $order_items_stmt_email->get_result();
                while ($item_email = $order_items_result_email->fetch_assoc()) {
                    $order_items_email[] = $item_email;
                }

                if (!empty($user_email) && !empty($order_items_email)) {
                    $subject = "Your ElsBee Data Order #{$order_id} is Completed!";

                    $message = "<div style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 8px;\">";
                    $message .= "<h2 style=\"color: #0056b3; margin-top: 0;\">Order Completed!</h2>";
                    $message .= "<p>Good news! Your order (ID: <strong>#{$order_id}</strong>) has been completed.</p>";

                    $message .= "<h3 style=\"margin-top: 20px; color: #555;\">Order Summary:</h3>";
                    $message .= "<ul style=\"list-style: none; padding: 0; border-top: 1px solid #eee; margin-top: 10px;\">";
                    foreach ($order_items_email as $item_email) {
                        $message .= "<li style=\"padding: 10px 0; border-bottom: 1px solid #eee;\">";
                        $message .= "<strong>" . htmlspecialchars($item_email['product_name']) . " (" . htmlspecialchars($item_email['data_size']) . ") x" . htmlspecialchars($item_email['quantity']) . "</strong> - &#8373;" . number_format($item_email['price'], 2);
                         if (!empty($item_email['beneficiary_number'])) {
                            $message .= "<br><small style=\"color: #777;\">Beneficiary: " . htmlspecialchars($item_email['beneficiary_number']) . "</small>";
                        }
                        $message .= "</li>";
                    }
                    $message .= "</ul>";

                    $message .= "<p style=\"font-size: 1.2em; font-weight: bold; margin-top: 20px; color: #28a745;\">Total: &#8373;" . number_format($order['total'], 2) . "</p>";

                    $message .= "<p>Thank you for your business!</p>";

                    // Send the email
                    $from_email = "<EMAIL>";
                    $from_name = "ElsBee Data";
                    $from_header = "{$from_name} <{$from_email}>";

                    $email_sent = send_email($user_email, $subject, $message, $from_header);

                    if (!$email_sent) {
                        error_log("Failed to send order completion email for Order #{$order_id} to {$user_email}");
                        $error .= " Failed to send order completion email.";
                    } else {
                         // Optionally add a success message to the admin view
                         $success .= " Order completion email sent to {$user_email}.";
                    }
                } else {
                     error_log("Order completion email skipped for Order #{$order_id}. User email or items missing.");
                     $error .= " Could not send order completion email (missing user email or order items).";
                }
            }
             // --- End Send Order Completion Email ---

            // Refresh order data after update
            $stmt = $conn->prepare('SELECT o.*, u.full_name FROM orders o JOIN users u ON o.user_id = u.id WHERE o.id = ?');
            $stmt->bind_param('i', $order_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $order = $result->fetch_assoc();
        } else {
            $error = 'Error updating order status.';
        }
    } else {
        $error = 'Invalid order status provided.';
    }
}

include '../includes/header.php';
?>

<div class="container py-5">
    <h2 class="mb-4">Order Details #<?php echo htmlspecialchars($order_id); ?></h2>

    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
    <?php endif; ?>

    <?php if ($order): ?>
        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title">Order Information</h4>
                <table class="table table-bordered table-striped">
                    <tbody>
                        <tr>
                            <th>Order ID:</th>
                            <td>#<?php echo $order['id']; ?></td>
                        </tr>
                        <tr>
                            <th>Reference Code:</th>
                            <td>
                                <code class="bg-light px-2 py-1 rounded"><?php echo htmlspecialchars($order['reference_code']); ?></code>
                            </td>
                        </tr>
                        <tr>
                            <th>Customer:</th>
                            <td><?php echo htmlspecialchars($order['full_name']); ?></td>
                        </tr>
                        <tr>
                            <th>Customer Number:</th>
                            <td><?php echo htmlspecialchars($order['user_number'] ?: 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Total Amount:</th>
                            <td>GHS <?php echo number_format($order['total'], 2); ?></td>
                        </tr>
                        <tr>
                            <th>Order Status:</th>
                            <td>
                                <?php
                                  $status = $order['status'];
                                  $badge_class = '';
                                  switch ($status) {
                                    case 'Pending': $badge_class = 'bg-warning'; break;
                                    case 'Processing': $badge_class = 'bg-info'; break;
                                    case 'Completed': $badge_class = 'bg-success'; break;
                                    case 'Cancelled': $badge_class = 'bg-danger'; break;
                                    default: $badge_class = 'bg-secondary';
                                  }
                                ?>
                                <span class="badge <?php echo $badge_class; ?>"><?php echo htmlspecialchars($status); ?></span>
                            </td>
                        </tr>
                        <tr>
                            <th>Payment Status:</th>
                            <td>
                                <?php
                                  $payment_status = $order['payment_status'];
                                  $badge_class = '';
                                  switch ($payment_status) {
                                    case 'Awaiting Manual Payment': $badge_class = 'bg-warning'; break;
                                    case 'PAID': $badge_class = 'bg-success'; break;
                                    case 'FAILED': $badge_class = 'bg-danger'; break;
                                    default: $badge_class = 'bg-secondary';
                                  }
                                ?>
                                <span class="badge <?php echo $badge_class; ?>"><?php echo htmlspecialchars($payment_status); ?></span>
                            </td>
                        </tr>
                         <tr>
                            <th>Order Date:</th>
                            <td><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></td>
                        </tr>
                    </tbody>
                </table>

                <h5 class="mt-4">Update Payment Status</h5>
                <form method="post" class="row g-3 align-items-center">
                    <div class="col-auto">
                        <label for="payment_status" class="col-form-label">Status:</label>
                    </div>
                    <div class="col-auto">
                        <select name="payment_status" id="payment_status" class="form-select" required>
                            <option value="Awaiting Manual Payment" <?php if ($order['payment_status'] === 'Awaiting Manual Payment') echo 'selected'; ?>>Awaiting Manual Payment</option>
                            <option value="PAID" <?php if ($order['payment_status'] === 'PAID') echo 'selected'; ?>>PAID</option>
                            <option value="FAILED" <?php if ($order['payment_status'] === 'FAILED') echo 'selected'; ?>>FAILED</option>
                        </select>
                    </div>
                     <div class="col-auto">
                        <button type="submit" name="update_payment_status" class="btn btn-primary">Update Status</button>
                    </div>
                </form>

                <h5 class="mt-4">Update Order Status</h5>
                 <form method="post" class="row g-3 align-items-center">
                    <div class="col-auto">
                        <label for="order_status" class="col-form-label">Status:</label>
                    </div>
                    <div class="col-auto">
                        <select name="order_status" id="order_status" class="form-select" required>
                            <option value="Pending" <?php if ($order['status'] === 'Pending') echo 'selected'; ?>>Pending</option>
                            <option value="Processing" <?php if ($order['status'] === 'Processing') echo 'selected'; ?>>Processing</option>
                            <option value="Completed" <?php if ($order['status'] === 'Completed') echo 'selected'; ?>>Completed</option>
                            <option value="Cancelled" <?php if ($order['status'] === 'Cancelled') echo 'selected'; ?>>Cancelled</option>
                        </select>
                    </div>
                     <div class="col-auto">
                        <button type="submit" name="update_order_status" class="btn btn-primary">Update Status</button>
                    </div>
                </form>

            </div>
        </div>

        <div class="card mb-4">
            <div class="card-body">
                <h4 class="card-title">Order Items</h4>
                 <?php if (!empty($order_items)): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped align-middle">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Size</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Beneficiary Number</th>
                                    <th>Full Name</th>
                                    <th>GH Card Number</th>
                                    <th>ID Type</th>
                                    <th>Region</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($order_items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                        <td><?php echo htmlspecialchars($item['data_size']); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td>GHS <?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                        <td><?php echo htmlspecialchars($item['beneficiary_number'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($item['full_name'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($item['gh_card_number'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($item['id_type'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($item['region'] ?? 'N/A'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                 <?php else: ?>
                    <p>No items found for this order.</p>
                 <?php endif; ?>
            </div>
        </div>

        <a href="orders.php" class="btn btn-secondary">Back to Orders</a>

    <?php else: ?>
        <div class="alert alert-danger">Order not found.</div>
        <a href="orders.php" class="btn btn-secondary">Back to Orders</a>
    <?php endif; ?>

</div>

<?php include '../includes/footer.php'; ?> 
