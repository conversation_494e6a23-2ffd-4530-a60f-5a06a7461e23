<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require_login();

// Determine the base path of the application
$base_path = '/datahub/';

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Check if product exists and is inactive
$check_stmt = $conn->prepare('SELECT active FROM products WHERE id = ?');
$check_stmt->bind_param('i', $id);
$check_stmt->execute();
$result = $check_stmt->get_result();
$product = $result->fetch_assoc();

if (!$product) {
    header('Location: products.php?error=not_found');
    exit;
}

if ($product['active']) {
    // Already active, no need to reactivate
    header('Location: products.php?error=already_active');
    exit;
}

// Start transaction
$conn->begin_transaction();

try {
    // Set product to active
    $stmt = $conn->prepare('UPDATE products SET active = 1 WHERE id = ?');
    $stmt->bind_param('i', $id);
    $stmt->execute();

    // Set all associated data sizes to active as well (assuming reactivation means all plans become active)
    $stmt = $conn->prepare('UPDATE data_sizes SET active = 1 WHERE product_id = ?');
    $stmt->bind_param('i', $id);
    $stmt->execute();

    $conn->commit();
    header('Location: products.php?reactivated=1');
} catch (Exception $e) {
    $conn->rollback();
    // Log the error for debugging
    error_log("Error reactivating product ID " . $id . ": " . $e->getMessage());
    header('Location: products.php?error=reactivation_failed');
}
exit; 