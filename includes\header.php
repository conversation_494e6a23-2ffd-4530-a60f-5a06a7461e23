<?php
// Check if a session is already active before starting a new one
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Determine the base path of the application
$base_path = '/'; // Adjust this if your application is in a different subdirectory

require 'db.php';
require 'time_manager.php';

// Check if website should be closed (only if time_manager is available)
if (function_exists('is_website_closed') && is_website_closed()) {
    // Only redirect if we're not already on the closed page or test page
    $current_page = basename($_SERVER['PHP_SELF']);
    $excluded_pages = ['closed.php', 'test_time.php', 'login.php', 'register.php', 'forgot_password.php', 'reset_password.php'];

    // Allow admin access even when closed
    $is_admin = isset($_SESSION['user']) && $_SESSION['user']['account_type'] === 'Admin';
    $is_admin_page = strpos($_SERVER['REQUEST_URI'], '/admin/') !== false;
    $is_setup_page = strpos($_SERVER['REQUEST_URI'], 'setup_closure_system.php') !== false;

    // Don't redirect if:
    // 1. Already on excluded pages
    // 2. Admin accessing admin pages
    // 3. Admin accessing setup page
    $should_redirect = !in_array($current_page, $excluded_pages) &&
                      !($is_admin && ($is_admin_page || $is_setup_page));

    if ($should_redirect) {
        header('Location: ' . $base_path . 'closed.php');
        exit;
    }
}

// Calculate cart item count
$cart_item_count = 0;
if (isset($_SESSION['cart'])) {
    foreach ($_SESSION['cart'] as $item) {
        $cart_item_count += $item['quantity'];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ElsBee Data - Buy Cheap Data Bundles Online | MTN, AirtelTigo, Telecel, MTN AFA</title>
  <link rel="icon" type="image/png" href="<?php echo $base_path; ?>image/icon-512.png">
  
  <!-- Primary Meta Tags -->
  <meta name="title" content="ElsBee Data - Buy Cheap Data Bundles Online | MTN, AirtelTigo, Telecel, MTN AFA">
  <meta name="description" content="Get seemless data bundles for all networks in Ghana. Buy MTN, AirtelTigo, Telecel, and MTN AFA data bundles at the best prices. Fast delivery, secure payments, and 20/7 support.">
  <meta name="keywords" content="cheap data bundles, buy data online, MTN data bundles, AirtelTigo data bundles, Telecel data bundles, MTN AFA bundles, data selling website, Ghana data bundles, cheap data site">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]"; ?>">
  <meta property="og:title" content="ElsBee Data - Buy Cheap Data Bundles Online | MTN, AirtelTigo, Telecel, MTN AFA">
  <meta property="og:description" content="Get seemless, cheap data bundles for all networks in Ghana. Buy MTN, AirtelTigo, Telecel, and MTN AFA data bundles at the best prices. Fast delivery, secure payments, and 20/7 support.">
  <meta property="og:image" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$base_path" ?>image/icon-512.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]"; ?>">
  <meta property="twitter:title" content="ElsBee Data - Buy Cheap Data Bundles Online | MTN, AirtelTigo, Telecel, MTN AFA">
  <meta property="twitter:description" content="Get seemless, cheap data bundles for all networks in Ghana. Buy MTN, AirtelTigo, Telecel, and MTN AFA data bundles at the best prices. Fast delivery, secure payments, and 20/7 support.">
  <meta property="twitter:image" content="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$base_path" ?>image/icon-512.png">
  <meta name="msvalidate.01" content="4729B4C3CFAC6BD21AC5D7663C16B4CF" />
  <meta name="google-site-verification" content="ehC1n4GPQCidjkcK9Hqt8s1zDZKsQsiWdRzXov9SWN8" />

  <!-- Schema.org markup for Google with SiteLinks SearchBox -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://elsbeedata.site/",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://elsbeedata.site/search.php?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  }
  </script>

  <!-- Schema.org markup for Google -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "ElsBee Data",
    "url": "https://elsbeedata.site",
    "logo": "https://elsbeedata.site/image/icon-512.png",
    "description": "Buy cheap data bundles online in Ghana. We provide seemless data bundles for MTN, AirtelTigo, Telecel, and MTN AFA at the best prices.",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Biabiani",
      "addressRegion": "Western North Region",
      "addressCountry": "GH"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+233599672113",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English"]
    },
    "sameAs": [
      "https://chat.whatsapp.com/CaiJeS1VgKJJBDz1dHWDVT"
    ],
    "openingHoursSpecification": [
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
        "opens": "08:00",
        "closes": "21:00"
      },
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": "Sunday",
        "opens": "11:00",
        "closes": "21:00"
      }
    ]
  }
  </script>

  <!-- Business information for search engines -->
  <meta name="business:contact_data:street_address" content="Biabiani">
  <meta name="business:contact_data:locality" content="Biabiani">
  <meta name="business:contact_data:region" content="Western North Region">
  <meta name="business:contact_data:postal_code" content="">
  <meta name="business:contact_data:country_name" content="Ghana">
  <meta name="business:contact_data:email" content="<EMAIL>">
  <meta name="business:contact_data:phone_number" content="+233599672113">
  <meta name="business:contact_data:website" content="https://elsbeedata.site">

  <!-- Canonical URL -->
  <link rel="canonical" href="<?php echo (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]"; ?>">
  
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <link rel="manifest" href="<?php echo $base_path; ?>manifest.json">
  <link href="<?php echo $base_path; ?>assets/css/style.css" rel="stylesheet">
  <style>
    /* Preloader styles */
    .preloader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.19); /* Semi-transparent background */
      backdrop-filter: blur(5px); /* Blur effect */
      -webkit-backdrop-filter: blur(5px); /* For Safari */
      display: flex;
      flex-direction: column; /* Stack items vertically */
      justify-content: center;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.5s ease-in-out;
    }
    
    .preloader.fade-out {
      opacity: 0;
      pointer-events: none;
    }
    
    .spinner {
      width: 60px;
      height: 60px;
      border: 5px solid rgba(106, 110, 231, 0.2);
      border-radius: 50%;
      border-top-color: var(--primary);
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 15px; /* Space between spinner and text */
    }
    
    .loading-text {
      color: var(--primary);
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 1px;
    }
    
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
    
    /* Basic styling for the floating button */
    .floating-cart-btn {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }
     /* Adjust badge position */
    .floating-cart-btn .badge {
        top: 0px;
        right: -5px;
        padding: 0.35em 0.65em;
    }
    /* Style for dynamic alerts */
    .alert-container {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 1050; /* Ensure it's above modals */
        width: 100%;
        max-width: 350px; /* Limit width */
    }
    .alert-container .alert {
        margin-bottom: 10px; /* Space between multiple alerts */
    }

    /* Styles for horizontally scrollable reviews */
    .reviews-container {
        display: flex;
        overflow-x: auto; /* Enable horizontal scrolling */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        scroll-snap-type: x mandatory; /* Snap to cards */
        padding-bottom: 15px; /* Add some padding for the scrollbar */
    }

    .review-card {
        flex: 0 0 auto; /* Prevent cards from shrinking */
        width: 80%; /* Set card width */
        margin-right: 15px; /* Space between cards */
        scroll-snap-align: start; /* Align cards to the start */
    }

    /* Adjust card width for larger screens */
    @media (min-width: 768px) {
        .review-card {
            width: 45%; /* Adjust width for medium screens */
        }
    }

     @media (min-width: 992px) {
        .review-card {
            width: 30%; /* Adjust width for large screens */
        }
    }

  </style>
</head>
<body>
<!-- Preloader -->
<div class="preloader" id="preloader">
  <div class="spinner"></div>
  <div class="loading-text">Loading...</div>
</div>

<script>
// Preloader functionality
document.addEventListener('DOMContentLoaded', function() {
  // Hide preloader when page is loaded
  const preloader = document.getElementById('preloader');
  if (preloader) {
    // Add a small delay to ensure smooth transition
    setTimeout(function() {
      preloader.classList.add('fade-out');
      // Remove preloader from DOM after animation completes
      setTimeout(function() {
        preloader.style.display = 'none';
      }, 500); // Match this to the CSS transition time
    }, 300); // Small delay before starting fade
  }
});

// Disable right click
document.addEventListener('contextmenu', function(e) {
    e.preventDefault();
    displayAlert('Right-click is disabled on this website.', 'warning');
    return false;
});

// Disable keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Prevent Ctrl+U (View Source)
    if (e.ctrlKey && e.key === 'u') {
        e.preventDefault();
        displayAlert('This action is not allowed.', 'warning');
        return false;
    }
    // Prevent Ctrl+S (Save)
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        displayAlert('This action is not allowed.', 'warning');
        return false;
    }
    // Prevent Ctrl+P (Print)
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        displayAlert('This action is not allowed.', 'warning');
        return false;
    }
    // Prevent Ctrl+Shift+I or F12 (Developer Tools)
    if ((e.ctrlKey && e.shiftKey && e.key === 'I') || e.key === 'F12') {
        e.preventDefault();
        displayAlert('This action is not allowed.', 'warning');
        return false;
    }
    // Prevent Ctrl+Shift+J (Developer Tools Console)
    if (e.ctrlKey && e.shiftKey && e.key === 'J') {
        e.preventDefault();
        displayAlert('This action is not allowed.', 'warning');
        return false;
    }
    // Prevent Ctrl+Shift+C (Developer Tools Element Inspector)
    if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        displayAlert('This action is not allowed.', 'warning');
        return false;
    }
});

// Real-time website closure checking
const GHANA_TIMEZONE_OFFSET = <?php echo get_ghana_timezone_offset(); ?>; // Ghana timezone offset in minutes

function getGhanaTime() {
  const now = new Date();
  const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
  return new Date(utc + (GHANA_TIMEZONE_OFFSET * 60000));
}

function isWebsiteClosed() {
  const ghanaTime = getGhanaTime();
  const hour = ghanaTime.getHours();
  return hour >= 21 || hour < 7; // Closed from 9 PM to 7 AM
}

function checkWebsiteStatus() {
  const ghanaTime = getGhanaTime();
  const hour = ghanaTime.getHours();
  const minute = ghanaTime.getMinutes();
  const currentPage = window.location.pathname.split('/').pop();
  const isAdminPage = window.location.pathname.includes('/admin/');
  const isAdmin = <?php echo (isset($_SESSION['user']) && $_SESSION['user']['account_type'] === 'Admin') ? 'true' : 'false'; ?>;

  if (isWebsiteClosed()) {
    // Website should be closed, redirect to closed page (but not for admins on admin pages)
    if (currentPage !== 'closed.php' && !(isAdmin && isAdminPage)) {
      window.location.href = '<?php echo $base_path; ?>closed.php';
    }
  } else {
    // Check if we're approaching closing time (5 minutes before 9 PM)
    if (hour === 20 && minute >= 55 && currentPage !== 'closed.php') {
      const minutesLeft = 60 - minute;
      displayAlert(`⏰ We'll be closing in ${minutesLeft} minute${minutesLeft !== 1 ? 's' : ''}. Please complete your transactions soon.`, 'warning');
    }
  }
}

// Check website status every minute for real-time switching
setInterval(checkWebsiteStatus, 60000);

// Also check immediately when page loads
document.addEventListener('DOMContentLoaded', function() {
  checkWebsiteStatus();
});
</script>

<!-- Container for dynamic alerts -->
<div class="alert-container"></div>

<nav class="navbar navbar-expand-lg navbar-dark">
  <div class="container">
    <a class="navbar-brand fw-bold" href="<?php echo $base_path; ?>index.php">
      <i class="bi bi-grid-3x3-gap"></i> ElsBee Data
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav me-auto">
        <li class="nav-item">
          <a class="nav-link" href="<?php echo $base_path; ?>mtn.php">
            <i class="bi bi-phone"></i> MTN
          </a>
        </li>
         <li class="nav-item">
          <a class="nav-link" href="<?php echo $base_path; ?>telecel.php">
            <i class="bi bi-phone"></i> Telecel
          </a>
        </li>
         <li class="nav-item">
          <a class="nav-link" href="<?php echo $base_path; ?>airteltigo.php">
            <i class="bi bi-phone"></i> AirtelTigo
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="<?php echo $base_path; ?>mtnafa.php">
            <i class="bi bi-phone"></i> MTN AFA
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="<?php echo $base_path; ?>index.php#how-it-works">
            <i class="bi bi-info-circle"></i> How It Works
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="<?php echo $base_path; ?>#testimonials">
            <i class="bi bi-chat-quote"></i> Testimonials
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="<?php echo $base_path; ?>#contact">
            <i class="bi bi-envelope"></i> Contact
          </a>
        </li>
      </ul>
      <ul class="navbar-nav">
        <?php if(isset($_SESSION['user'])): ?>
          <?php if(isset($_SESSION['user']['account_type']) && $_SESSION['user']['account_type'] === 'Admin'): ?>
            <li class="nav-item">
              <a class="nav-link" href="<?php echo $base_path; ?>admin/dashboard.php">
                <i class="bi bi-speedometer2"></i> Admin
              </a>
            </li>
          <?php endif; ?>
          <li class="nav-item d-none d-lg-block"> <!-- Hide on mobile, show on large screens -->
            <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#cartModal">
              <i class="bi bi-cart3"></i> Cart
            </a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($_SESSION['user']['full_name']); ?>
            </a>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
              <li>
                <a class="dropdown-item" href="<?php echo $base_path; ?>profile/index.php">
                  <i class="bi bi-person"></i> Profile
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="<?php echo $base_path; ?>orders/index.php">
                  <i class="bi bi-bag"></i> My Orders
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="<?php echo $base_path; ?>reviews/index.php">
                  <i class="bi bi-star"></i> Reviews
                </a>
              </li>
              <li><hr class="dropdown-divider"></li>
              <li>
                <a class="dropdown-item" href="<?php echo $base_path; ?>logout.php">
                  <i class="bi bi-box-arrow-right"></i> Logout
                </a>
              </li>
            </ul>
          </li>

        <?php else: ?>
          <li class="nav-item">
            <a class="nav-link" href="<?php echo $base_path; ?>login.php">
              <i class="bi bi-box-arrow-in-right"></i> Login
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="<?php echo $base_path; ?>register.php">
              <i class="bi bi-person-plus"></i> Register
            </a>
          </li>
        <?php endif; ?>
      </ul>
    </div>
  </div>
</nav>

<!-- Floating Cart Icon for Mobile (Hidden on large screens) -->
<div class="d-block d-lg-none">
    <a href="#" class="btn btn-primary rounded-circle floating-cart-btn shadow" data-bs-toggle="modal" data-bs-target="#cartModal">
        <i class="bi bi-cart3 fs-5"></i>
        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger cart-count">
            <?php echo $cart_item_count; ?> <!-- Display actual cart item count -->
            <span class="visually-hidden">items in cart</span>
        </span>
    </a>
</div>

<div class="content">

<!-- Cart Modal -->
<div class="modal fade" id="cartModal" tabindex="-1" aria-labelledby="cartModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="cartModalLabel"><i class="bi bi-cart3 me-2"></i>Your Cart</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Cart content will be loaded here via AJAX -->
        Loading cart...
      </div>
      <div class="modal-footer justify-content-between">
        <h5 class="mb-0">Total: <span class="text-success" id="cart-modal-total"></span></h5>
        <a href="<?php echo $base_path; ?>cart/checkout.php" class="btn btn-primary">Proceed to Checkout</a>
      </div>
    </div>
  </div>
</div>

<!-- Remove Confirmation Modal -->
<div class="modal fade" id="removeConfirmModal" tabindex="-1" aria-labelledby="removeConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-sm">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="removeConfirmModalLabel">Confirm Removal</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Are you sure you want to remove this item from your cart?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmRemoveBtn">Remove</button>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+AM1Q3EwE1z4/pxsb7F+5aB9N3y+M"
        crossorigin="anonymous"></script>





<script>
// Function to display a dynamic Bootstrap alert
function displayAlert(message, type = 'success') {
    const alertContainer = document.querySelector('.alert-container');
    if (!alertContainer) {
        console.error('Alert container not found!');
        return;
    }

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    const alertElement = document.createElement('div');
    alertElement.innerHTML = alertHtml;
    alertContainer.appendChild(alertElement);

    // Automatically dismiss the alert after 5 seconds
    setTimeout(() => {
        const bootstrapAlert = bootstrap.Alert.getInstance(alertElement.querySelector('.alert'));
        if (bootstrapAlert) {
            bootstrapAlert.hide(); // Use Bootstrap's hide method for fade effect
            // Remove the element after it fades out
            alertElement.addEventListener('hidden.bs.alert', function () {
                alertElement.remove();
            });
        } else {
             // Fallback if Bootstrap's JS is not fully loaded or alert not initialized
             alertElement.remove();
        }
    }, 5000); // 5000 milliseconds = 5 seconds
}
</script>

</div>
</body>
</html> 
