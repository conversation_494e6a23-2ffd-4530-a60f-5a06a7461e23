<?php
session_start();

if (!isset($_SESSION['user'])) {
    http_response_code(401); // Unauthorized
    echo json_encode(['success' => false, 'error' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
    exit;
}

$cart_key = isset($_POST['cart_key']) ? $_POST['cart_key'] : null;

if ($cart_key === null) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'error' => 'Invalid cart item key.']);
    exit;
}

$cart = $_SESSION['cart'] ?? [];

if (isset($cart[$cart_key])) {
    unset($cart[$cart_key]); // Remove the item from the cart array
    $_SESSION['cart'] = $cart; // Update the session cart

    // Calculate and return the new cart item count
    $new_cart_item_count = 0;
    foreach ($_SESSION['cart'] as $item) {
        $new_cart_item_count += $item['quantity'];
    }

    echo json_encode(['success' => true, 'message' => 'Item removed successfully.', 'cart_count' => $new_cart_item_count]);
    exit;
} else {
    http_response_code(404); // Not Found
    echo json_encode(['success' => false, 'error' => 'Item not found in cart.']);
    exit;
}
?> 