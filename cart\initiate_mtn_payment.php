<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';
require '../includes/mtn_payment.php';

// Determine the base path of the application
$base_path = '/datahub/';

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'User not logged in']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['amount']) || !isset($input['order_id']) || !isset($input['mtn_number'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

// Validate MTN number format
$mtn_number = $input['mtn_number'];
if (!preg_match('/^(233|0)[0-9]{9}$/', $mtn_number)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid MTN number format']);
    exit;
}

// Format MTN number to international format if needed
if (strpos($mtn_number, '0') === 0) {
    $mtn_number = '233' . substr($mtn_number, 1);
}

// MTN API credentials - Replace these with your actual credentials
$mtn_client_id = 'YOUR_CLIENT_ID';
$mtn_client_secret = 'YOUR_CLIENT_SECRET';
$mtn_subscription_key = 'YOUR_SUBSCRIPTION_KEY';
$mtn_environment = 'sandbox'; // Change to 'production' for live environment

$mtn = new MTNPayment($mtn_client_id, $mtn_client_secret, $mtn_subscription_key, $mtn_environment);

// Prepare payment request
$amount = floatval($input['amount']);
$order_id = $input['order_id'];
$currency = 'EUR'; // MTN MoMo uses EUR as the currency code
$external_id = 'ELS_' . $order_id;
$payer_message = 'Payment for ElsBee Data order #' . $order_id;
$payee_note = 'Data bundle purchase';
$callback_url = $base_path . 'cart/mtn_callback.php';

// Update the payer's number in the request
$mtn->payer_number = $mtn_number;

// Initiate payment
$result = $mtn->requestToPay($amount, $currency, $external_id, $payer_message, $payee_note, $callback_url);

if ($result['success']) {
    // Store transaction details in database
    $stmt = $conn->prepare('INSERT INTO payment_transactions (order_id, transaction_id, payment_method, amount, status, created_at) VALUES (?, ?, ?, ?, ?, NOW())');
    $payment_method = 'mtn_momo';
    $status = 'PENDING';
    $stmt->bind_param('issds', $order_id, $result['transaction_id'], $payment_method, $amount, $status);
    $stmt->execute();
    
    // Update order status
    $stmt = $conn->prepare('UPDATE orders SET payment_status = ? WHERE id = ?');
    $payment_status = 'PENDING';
    $stmt->bind_param('si', $payment_status, $order_id);
    $stmt->execute();
}

// Return the result
header('Content-Type: application/json');
echo json_encode($result);
?> 