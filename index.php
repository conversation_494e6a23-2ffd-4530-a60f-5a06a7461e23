<?php include 'includes/header.php'; ?>

<!-- Hero Section - Optimized for Keywords -->
<section class="hero-section text-white text-center py-5" aria-label="Main Banner">
  <div class="container">
    <h1 class="display-4 fw-bold">Buy Cheap Data Bundles Online in Ghana</h1>
    <p class="lead">Get data bundles for MTN, Airtel, Telecel, and MTN AFA at the best prices in Ghana. Your trusted data selling website with 20/7 support and seemless delivery.</p>
    <a href="#data-services" class="btn btn-lg mt-3" aria-label="View Available Data Bundles">View Available Data Bundles</a>
  </div>
</section>

<!-- Features Section -->
<section class="container py-4" aria-label="Key Features">
  <div class="row g-4">
    <div class="col-md-4 text-center">
      <div class="card h-100 shadow-sm">
        <div class="card-body">
          <div class="mb-3"><span class="fs-1 custom-color-primary" aria-hidden="true"><i class="bi bi-lightning-charge"></i></span></div>
          <h2 class="h5 card-title">Seemless Data Delivery</h2>
          <p class="card-text">Get your data bundle in 10 - 30 minutes time after payment confirmation.</p>
        </div>
      </div>
    </div>
    <div class="col-md-4 text-center">
      <div class="card h-100 shadow-sm">
        <div class="card-body">
          <div class="mb-3"><span class="fs-1 custom-color-primary" aria-hidden="true"><i class="bi bi-shield-lock"></i></span></div>
          <h2 class="h5 card-title">Secure Payment Processing</h2>
          <p class="card-text">Safe and secure payment processing with Mobile Money. Your transactions are protected.</p>
        </div>
      </div>
    </div>
    <div class="col-md-4 text-center">
      <div class="card h-100 shadow-sm">
        <div class="card-body">
          <div class="mb-3"><span class="fs-1 custom-color-primary" aria-hidden="true"><i class="bi bi-headset"></i></span></div>
          <h2 class="h5 card-title">20/7 Customer Support</h2>
          <p class="card-text">Round-the-clock customer support to assist you with any queries or issues.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Data Services Section - Optimized for Keywords -->
<section class="section-alt py-5" id="data-services" aria-label="Available Data Bundles">
  <div class="container">
    <h2 class="text-center mb-4">Browse Our Cheap Data Bundles in Ghana</h2>
    <div class="row g-4 justify-content-center">
      <div class="col-10 col-md-6 col-lg-4">
        <div class="card data-service-card">
          <img src="https://upload.wikimedia.org/wikipedia/commons/9/93/New-mtn-logo.jpg" class="card-img-top" alt="Buy Cheap MTN Data Bundles in Ghana - Best MTN Data Deals" loading="lazy">
          <div class="card-body text-center">
            <h3 class="h5 card-title">MTN Data Bundle</h3>
            <p class="card-text">Get the best MTN data bundles from 1GB to 100GB at competitive prices</p>
            <a href="<?php echo $base_path; ?>mtn.php" class="btn" aria-label="Buy MTN Data Bundles">Buy MTN Data</a>
          </div>
        </div>
      </div>
      <div class="col-10 col-md-6 col-lg-4">
        <div class="card data-service-card">
          <img src="https://citinewsroom.com/wp-content/uploads/2022/04/Fluid-logo.png" class="card-img-top" alt="Buy Cheap AirtelTigo Data Bundles in Ghana - Best Airtel Data Deals" loading="lazy">
          <div class="card-body text-center">
            <h3 class="h5 card-title">Airtel Data Bundle</h3>
            <p class="card-text">Get the best Airtel data bundles from 1GB to 100GB at competitive prices</p>
            <a href="<?php echo $base_path; ?>airteltigo.php" class="btn" aria-label="Buy AirtelTigo Data Bundles">Buy AirtelTigo Data</a>
          </div>
        </div>
      </div>
      <div class="col-10 col-md-6 col-lg-4">
        <div class="card data-service-card">
          <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTl4R7lA1tlSlrBzf9OrDXIswYytfI7TfvC0w&s" class="card-img-top" alt="Buy Cheap Telecel Data Bundles in Ghana - Best Telecel Data Deals" loading="lazy">
          <div class="card-body text-center">
            <h3 class="h5 card-title">Telecel Data Bundle</h3>
            <p class="card-text">Get the best Telecel data bundles from 1GB to 100GB at competitive prices</p>
            <a href="<?php echo $base_path; ?>telecel.php" class="btn" aria-label="Buy Telecel Data Bundles">Buy Telecel Data</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- How It Works Section -->
<section class="container py-5" aria-label="How to Buy Data">
  <h2 class="text-center mb-4" id="how-it-works">How to Buy Data Bundles Online</h2>
  <div class="row justify-content-center">
    <div class="col-12 col-md-8">
      <div class="row text-center">
        <div class="col-4">
          <div class="circle mb-2" aria-hidden="true">1</div>
          <h3 class="h6">Select Your Data Bundle</h3>
          <p class="small">Choose from our wide range of cheap data bundles for all networks.</p>
        </div>
        <div class="col-4">
          <div class="circle mb-2" aria-hidden="true">2</div>
          <h3 class="h6">Make Secure Payment</h3>
          <p class="small">Pay securely using Mobile Money for your selected data bundle.</p>
        </div>
        <div class="col-4">
          <div class="circle mb-2" aria-hidden="true">3</div>
          <h3 class="h6">Seemless Data Delivery</h3>
          <p class="small">Receive your data bundle 10 - 30 minutes after payment confirmation.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Stats Section -->
<section class="section-alt py-4" aria-label="Our Statistics">
  <div class="container">
    <div class="row text-center g-4">
      <div class="col-6 col-md-3">
        <div class="card shadow-sm">
          <div class="card-body">
            <h3 class="custom-color-primary-dark" aria-label="Over 2000 happy customers">150+</h3>
            <p class="mb-0">Happy Customers</p>
          </div>
        </div>
      </div>
      <div class="col-6 col-md-3">
        <div class="card shadow-sm">
          <div class="card-body">
            <h3 class="custom-color-primary-dark" aria-label="99 percent successful deliveries">99%</h3>
            <p class="mb-0">Successful Deliveries</p>
          </div>
        </div>
      </div>
      <div class="col-6 col-md-3">
        <div class="card shadow-sm">
          <div class="card-body">
            <h3 class="custom-color-primary-dark" aria-label="24/7 customer support">20/7</h3>
            <p class="mb-0">Customer Support</p>
          </div>
        </div>
      </div>
      <div class="col-6 col-md-3">
        <div class="card shadow-sm">
          <div class="card-body">
            <h3 class="custom-color-primary-dark" aria-label="Fast data delivery">Fast</h3>
            <p class="mb-0">Seemless Delivery</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section class="container py-5" id="testimonials" aria-label="Customer Reviews">
  <h2 class="text-center mb-4">What Our Customers Say About Our Data Bundles</h2>
  <div class="row justify-content-center">
    <div class="col-12 col-md-8">
      <?php
      require_once 'includes/db.php'; // Ensure database connection is available

      // Fetch reviews and user names
      $reviews_sql = "SELECT r.*, u.full_name, p.name as product_name FROM reviews r JOIN users u ON r.user_id = u.id JOIN products p ON r.product_id = p.id ORDER BY r.created_at DESC LIMIT 5"; // Fetch latest 5 reviews and product name
      $reviews_result = $conn->query($reviews_sql);

      if ($reviews_result->num_rows > 0) {
          ?>
          <div class="reviews-container">
          <?php
          while($review = $reviews_result->fetch_assoc()) {
              // Display each review
              ?>
              <div class="review-card card p-4 mb-3 shadow-sm">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                      <div>
                          <h6 class="mb-0"><?php echo htmlspecialchars($review['full_name']); ?></h6>
                          <small class="text-muted">Product: <?php echo htmlspecialchars($review['product_name']); ?></small>
                      </div>
                      <div class="star-rating d-flex">
                          <?php
                          $rating = $review['rating'];
                          for ($i = 1; $i <= 5; $i++) {
                              if ($i <= $rating) {
                                  echo '<i class="bi bi-star-fill text-warning"></i>'; // Filled star
                              } else {
                                  echo '<i class="bi bi-star text-warning"></i>'; // Empty star
                              }
                          }
                          ?>
                      </div>
                  </div>
                  <hr>
                  <p class="mb-0"><?php echo htmlspecialchars($review['review']); ?></p>
                  <small class="text-muted mt-2">Reviewed on: <?php echo date('F j, Y', strtotime($review['created_at'])); ?></small>
              </div>
              <?php
          }
          ?>
          </div>
          <?php
      } else {
          // Display message if no reviews found
          ?>
          <div class="card p-4 text-center">
            <p class="mb-0">No reviews yet. Be the first to share your experience!</p>
          </div>
          <?php
      }
      $conn->close(); // Close connection after fetching reviews
      ?>
    </div>
  </div>
</section>

<!-- Contact Section -->
<section class="section-alt py-5" id="contact">
  <div class="container">
    <h2 class="text-center mb-4">Contact ElsBee Data</h2>
    <div class="row justify-content-center">
      <div class="col-12 col-md-6">
        <form id="contact-form" action="https://formspree.io/f/mrbqdlea" method="POST">
          <div class="mb-3">
            <input type="text" class="form-control" name="name" placeholder="Your Name" required>
          </div>
          <div class="mb-3">
            <input type="email" class="form-control" name="email" placeholder="Your Email" required>
          </div>
          <div class="mb-3">
            <textarea class="form-control" name="message" rows="3" placeholder="Your Message" required></textarea>
          </div>
          <button type="submit" class="btn btn-primary">Send Message</button>
        </form>
        <div class="mt-4">
          <p><i class="bi bi-geo-alt"></i> Biabiani, Western North Region, Ghana</p>
          <p><i class="bi bi-telephone"></i> +233599672113</p>
          <p><i class="bi bi-envelope"></i> <EMAIL></p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Thank You Modal -->
<div class="modal fade" id="contactThankYouModal" tabindex="-1" aria-labelledby="contactThankYouModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="contactThankYouModalLabel">Thank You!</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Thanks for contacting us. We'll reach out to you soon.
        <br>
        <br>
        <center><button type="button" class="btn btn-success" data-bs-dismiss="modal">Close</button></center>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const contactForm = document.getElementById('contact-form');
  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const formData = new FormData(contactForm);
      fetch('https://formspree.io/f/mnndvepk', {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json'
        }
      })
      .then(response => {
        if (response.ok) {
          // Show the thank you modal
          const thankYouModal = new bootstrap.Modal(document.getElementById('contactThankYouModal'));
          thankYouModal.show();
          contactForm.reset();
        } else {
          response.json().then(data => {
            alert(data.errors ? data.errors.map(e => e.message).join(', ') : 'There was a problem submitting your message.');
          });
        }
      })
      .catch(() => {
        alert('There was a problem submitting your message.');
      });
    });
  }
});
</script>

<?php include 'includes/footer.php'; ?> 