# ElsBee Data - Time-Based Website Closure System

## Overview
This system automatically closes your website from **9 PM to 7 AM Ghana time** and reopens it automatically. Users will see a beautiful "closed" page during closure hours.

## Features Implemented

### ✅ **Automatic Time-Based Closure**
- **Closed Hours**: 9:00 PM - 6:59 AM Ghana time (10 hours)
- **Open Hours**: 7:00 AM - 8:59 PM Ghana time (14 hours)
- **Timezone**: Ghana (GMT+0 / Africa/Accra)

### ✅ **Hybrid Approach**
- **Server-side**: P<PERSON> checks time and redirects during closed hours
- **Client-side**: JavaScript provides real-time switching without page refresh
- **Seamless**: Users automatically see changes at exactly 9 PM and 7 AM

### ✅ **Beautiful Closed Page**
- Matches your website's purple gradient design
- Professional animated background
- Real-time countdown to opening
- Current Ghana time display
- Responsive design for all devices

### ✅ **Smart Features**
- **Warning System**: Users get notified 5 minutes before closing
- **Real-time Updates**: Countdown and time updates every second
- **Auto-redirect**: Automatic switching between closed/open states
- **All Pages Covered**: Every page on your site is affected

### ✅ **User Experience**
- Users at 9 PM automatically see closed page (no refresh needed)
- Users at 7 AM automatically see main site (no refresh needed)
- Beautiful, professional closed page with your branding
- Clear information about when you'll reopen

## Files Created/Modified

### New Files:
1. **`includes/time_manager.php`** - Core time management functions
2. **`closed.php`** - Beautiful closed page
3. **`test_time.php`** - Test page to verify system works
4. **`CLOSURE_SYSTEM_README.md`** - This documentation

### Modified Files:
1. **`includes/header.php`** - Added time checking and real-time JavaScript

## How It Works

### Server-Side (PHP)
```php
// In includes/header.php
if (is_website_closed()) {
    header('Location: /closed.php');
    exit;
}
```

### Client-Side (JavaScript)
```javascript
// Real-time checking every minute
setInterval(checkWebsiteStatus, 60000);

// Automatic redirect when time changes
if (isWebsiteClosed()) {
    window.location.href = '/closed.php';
}
```

## Testing the System

### Test Current Status:
Visit: `http://yoursite.com/test_time.php`

This page shows:
- Current Ghana time
- Website status (OPEN/CLOSED)
- Next opening/closing times
- Test results for different hours

### Manual Testing:
1. **During Open Hours (7 AM - 9 PM)**: Website works normally
2. **During Closed Hours (9 PM - 7 AM)**: Redirects to closed page
3. **At 9 PM exactly**: Users automatically see closed page
4. **At 7 AM exactly**: Users automatically see main site

## Customization Options

### Change Closure Hours:
Edit `includes/time_manager.php`:
```php
// Current: Closed 9 PM - 7 AM
return ($current_hour >= 21 || $current_hour < 7);

// Example: Closed 10 PM - 6 AM
return ($current_hour >= 22 || $current_hour < 6);
```

### Change Warning Time:
Edit `includes/header.php`:
```javascript
// Current: 5 minutes before closing
if (hour === 20 && minute >= 55) {

// Example: 10 minutes before closing
if (hour === 20 && minute >= 50) {
```

### Customize Closed Message:
Edit `closed.php` line 189:
```html
<p class="closed-message">
  Your custom message here
</p>
```

## Technical Details

### Timezone Handling:
- Uses PHP's `DateTimeZone('Africa/Accra')` for accurate Ghana time
- JavaScript calculates Ghana time from UTC offset
- Handles daylight saving time automatically

### Performance:
- Minimal server load (simple time calculations)
- Client-side checks only every minute
- No database queries for time checking

### Security:
- No security vulnerabilities introduced
- Maintains existing authentication system
- Safe redirects only to internal pages

## Troubleshooting

### If System Doesn't Work:
1. Check `test_time.php` for current status
2. Verify server timezone settings
3. Check browser console for JavaScript errors
4. Ensure all files are uploaded correctly

### Common Issues:
- **Wrong timezone**: Server might be in different timezone
- **Caching**: Clear browser cache if changes don't appear
- **File permissions**: Ensure PHP files are readable

## Support

The system is now fully implemented and ready to use! It will automatically:
- Close your website at 9 PM Ghana time
- Show the beautiful closed page to all visitors
- Reopen at 7 AM Ghana time
- Provide real-time updates and smooth transitions

Your website will now operate exactly as requested with professional, automatic time-based closure!
