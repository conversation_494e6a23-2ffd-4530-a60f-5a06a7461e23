# ElsBee Data

## Project Description

ElsBee Data is a full-stack responsive web application built with PHP and MySQL for buying data bundles online (MTN, Airtel, Telecel, MTN AFA). It allows users to register, browse products, add to cart, place orders, and make payments via a manual mobile money transfer system. The admin panel provides functionalities to manage users and orders, including updating order and payment statuses and sending SMS notifications upon payment confirmation.

## Features

- User registration and login
- Browse and purchase data bundles for multiple networks (MTN, Airtel, Telecel, MTN AFA)
- Shopping cart functionality
- Manual payment system via Mobile Money (MTN, AirtelTigo, Telecel)
- Unique 4-digit reference codes for manual payments
- Admin dashboard for order and user management
- Update order status (Pending, Processing, Completed, Cancelled)
- Update payment status (Awaiting Manual Payment, PAID, FAILED)
- Send SMS notifications to customers upon payment confirmation (via USMS-GH API)
- User profile page

## Technologies Used

- Backend: PHP
- Database: MySQL
- Frontend: HTML, CSS, JavaScript, Bootstrap (for styling)
- SMS Gateway Integration: USMS-GH HTTP API

## Prerequisites

Before setting up the project, ensure you have the following installed:

- A web server with PHP support (e.g., Apache, Nginx)
- MySQL database server
- PHP (version 7.4 or higher recommended)
- Composer (if you plan to manage PHP dependencies, although currently, none are explicitly required)


## Manual Payment Process

When a customer places an order, they will be shown instructions for manual payment, including the Mobile Money number (0599672113) and name (Joseph Atta Brown) to send the payment to. A unique 4-digit reference code is generated for each order, which the customer should include in the payment description/reference field. Administrators verify the payment manually and update the payment status in the admin panel.

## SMS Notifications

Upon an administrator changing an order's payment status to 'PAID', the system attempts to send an SMS confirmation to the customer's registered phone number using the USMS-GH API. Ensure the API token is correct and the Sender ID is approved.


## License

[Specify your license here, e.g., MIT, GPL, etc.] 