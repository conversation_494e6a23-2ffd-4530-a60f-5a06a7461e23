<?php
session_start();
require '../includes/db.php';
include '../includes/header.php';

$cart = $_SESSION['cart'] ?? [];
$total = 0;
?>
<div class="container py-5">
  <h2 class="mb-4 text-center">My Cart</h2>
  <?php if(isset($_GET['added'])): ?><div class="alert alert-success">Item added to cart!</div><?php endif; ?>
  <?php if(empty($cart)): ?>
    <div class="alert alert-info text-center">Your cart is empty.</div>
  <?php else: ?>
    <form method="post" action="checkout.php">
    <div class="table-responsive">
      <table class="table align-middle">
        <thead>
          <tr>
            <th>Product</th>
            <th>Data Size</th>
            <th>Quantity</th>
            <th>Price</th>
            <th>Remove</th>
          </tr>
        </thead>
        <tbody>
        <?php foreach($cart as $key => $item):
          // Fetch product name (since it's not stored in the cart session item)
          $stmt = $conn->prepare('SELECT name FROM products WHERE id = ?');
          $stmt->bind_param('i', $item['product_id']);
          $stmt->execute();
          $result = $stmt->get_result();
          $product = $result->fetch_assoc();
          $product_name = $product['name'] ?? 'Unknown Product';

          // Use the price and size stored in the cart item
          $price = $item['price'] ?? 0;
          $data_size_display = $item['size'] ?? 'N/A';

          $line_total = $price * $item['quantity'];
          $total += $line_total;
        ?>
          <tr>
            <td><?php echo htmlspecialchars($product_name); ?></td>
            <td><?php echo htmlspecialchars($data_size_display); ?></td>
            <td><?php echo $item['quantity']; ?></td>
            <td>
              <div class="d-flex flex-column">
                <span class="text-success">
                  <?php echo htmlspecialchars($item['size']); ?> - &#8373;<?php echo number_format($item['price'], 2); ?>
                </span>
                <span class="text-muted">x<?php echo $item['quantity']; ?></span>
              </div>
              <?php if (!empty($item['beneficiary_number'])): ?>
                <small class="text-muted">Beneficiary: <?php echo htmlspecialchars($item['beneficiary_number']); ?></small>
              <?php endif; ?>
            </td>
            <td>&#8373;<?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
            <td><a href="remove.php?key=<?php echo urlencode($key); ?>" class="btn btn-sm btn-danger">Remove</a></td>
          </tr>
        <?php endforeach; ?>
        </tbody>
      </table>
    </div>
   
    <div class="text-end">
      <a href="checkout.php" class="btn btn-primary">Proceed to Checkout</a>
    </div>
    </form>
  <?php endif; ?>
</div>
<?php include '../includes/footer.php'; ?> 