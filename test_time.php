<?php
// Test page for time management system
require 'includes/time_manager.php';

echo "<h1>ElsBee Data - Time Management Test</h1>";
echo "<p><strong>Current Ghana Time:</strong> " . get_ghana_time_display() . "</p>";
echo "<p><strong>Website Status:</strong> " . (is_website_closed() ? "CLOSED" : "OPEN") . "</p>";

$next_opening = get_next_opening_time();
$next_closing = get_next_closing_time();
$time_info = get_time_until_status_change();

echo "<p><strong>Next Opening:</strong> " . $next_opening->format('g:i A \o\n l, F j, Y') . "</p>";
echo "<p><strong>Next Closing:</strong> " . $next_closing->format('g:i A \o\n l, F j, Y') . "</p>";
echo "<p><strong>Next Action:</strong> " . ucfirst($time_info['next_action']) . " in " . $time_info['seconds'] . " seconds</p>";
echo "<p><strong>Ghana Timezone Offset:</strong> " . get_ghana_timezone_offset() . " minutes</p>";

// Test different hours
echo "<h2>Testing Different Hours:</h2>";
$test_hours = [6, 7, 12, 20, 21, 23];
foreach ($test_hours as $hour) {
    $test_time = new DateTime('now', new DateTimeZone('Africa/Accra'));
    $test_time->setTime($hour, 0, 0);
    $test_hour = (int)$test_time->format('H');
    $is_closed = ($test_hour >= 21 || $test_hour < 7);
    echo "<p>Hour {$hour}:00 - " . ($is_closed ? "CLOSED" : "OPEN") . "</p>";
}
?>
