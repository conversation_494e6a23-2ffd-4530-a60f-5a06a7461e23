<?php
/**
 * Time Management System for ElsBee Data
 * Handles Ghana timezone time checking for website closure
 * Closed: 9 PM - 7 AM Ghana time (GMT+0)
 * Open: 7 AM - 9 PM Ghana time (GMT+0)
 */

/**
 * Get current time in Ghana timezone
 * @return DateTime Current time in Ghana timezone
 */
function get_ghana_time() {
    // Ghana is GMT+0 (same as UTC)
    $ghana_timezone = new DateTimeZone('Africa/Accra');
    return new DateTime('now', $ghana_timezone);
}

/**
 * Check if the website should be closed
 * @return bool True if website should be closed, false if open
 */
function is_website_closed() {
    global $conn;

    // Check for admin override first
    $admin_override = get_admin_closure_override();
    if ($admin_override !== null) {
        return $admin_override;
    }

    // Default time-based closure
    $ghana_time = get_ghana_time();
    $current_hour = (int)$ghana_time->format('H'); // 24-hour format

    // Closed from 21:00 (9 PM) to 06:59 (6:59 AM)
    // Open from 07:00 (7 AM) to 20:59 (8:59 PM)
    return ($current_hour >= 21 || $current_hour < 7);
}

/**
 * Get admin closure override setting
 * @return bool|null True if manually closed, false if manually opened, null if no override
 */
function get_admin_closure_override() {
    global $conn;

    $stmt = $conn->prepare("SELECT setting_value FROM settings WHERE setting_key = 'admin_closure_override'");
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        $value = $row['setting_value'];
        if ($value === 'closed') {
            return true;
        } elseif ($value === 'open') {
            return false;
        }
    }

    return null; // No override
}

/**
 * Set admin closure override
 * @param string $status 'closed', 'open', or 'auto' for automatic
 * @param int $admin_id ID of the admin making the change
 * @return bool Success status
 */
function set_admin_closure_override($status, $admin_id) {
    global $conn;

    $valid_statuses = ['closed', 'open', 'auto'];
    if (!in_array($status, $valid_statuses)) {
        return false;
    }

    $conn->begin_transaction();

    try {
        // Update or insert the override setting
        if ($status === 'auto') {
            // Remove override to return to automatic mode
            $stmt = $conn->prepare("DELETE FROM settings WHERE setting_key = 'admin_closure_override'");
            $stmt->execute();
        } else {
            $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value, description) VALUES ('admin_closure_override', ?, 'Admin manual override for website closure') ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
            $stmt->bind_param('s', $status);
            $stmt->execute();
        }

        // Log the activity
        log_admin_closure_activity($admin_id, $status);

        $conn->commit();
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        return false;
    }
}

/**
 * Log admin closure activity
 * @param int $admin_id ID of the admin
 * @param string $action Action taken
 */
function log_admin_closure_activity($admin_id, $action) {
    global $conn;

    $description = "Admin manually set website to: " . $action;
    $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, description, created_at) VALUES (?, 'website_closure', ?, NOW())");
    $stmt->bind_param('is', $admin_id, $description);
    $stmt->execute();
}

/**
 * Get current closure status with reason
 * @return array Status information
 */
function get_closure_status_info() {
    $admin_override = get_admin_closure_override();
    $is_closed = is_website_closed();

    if ($admin_override !== null) {
        return [
            'is_closed' => $is_closed,
            'reason' => $admin_override ? 'manually_closed' : 'manually_opened',
            'message' => $admin_override ? 'Website manually closed by admin' : 'Website manually opened by admin'
        ];
    } else {
        $ghana_time = get_ghana_time();
        $current_hour = (int)$ghana_time->format('H');

        if ($is_closed) {
            return [
                'is_closed' => true,
                'reason' => 'scheduled_closure',
                'message' => 'Website closed according to schedule (9 PM - 7 AM)'
            ];
        } else {
            return [
                'is_closed' => false,
                'reason' => 'scheduled_open',
                'message' => 'Website open according to schedule (7 AM - 9 PM)'
            ];
        }
    }
}

/**
 * Get the next opening time
 * @return DateTime Next opening time (7 AM Ghana time)
 */
function get_next_opening_time() {
    $ghana_time = get_ghana_time();
    $current_hour = (int)$ghana_time->format('H');
    
    // If it's before 7 AM, opening is today at 7 AM
    if ($current_hour < 7) {
        $opening_time = clone $ghana_time;
        $opening_time->setTime(7, 0, 0);
    } else {
        // If it's after 9 PM, opening is tomorrow at 7 AM
        $opening_time = clone $ghana_time;
        $opening_time->add(new DateInterval('P1D')); // Add 1 day
        $opening_time->setTime(7, 0, 0);
    }
    
    return $opening_time;
}

/**
 * Get the next closing time
 * @return DateTime Next closing time (9 PM Ghana time)
 */
function get_next_closing_time() {
    $ghana_time = get_ghana_time();
    $current_hour = (int)$ghana_time->format('H');
    
    // If it's before 9 PM today, closing is today at 9 PM
    if ($current_hour < 21) {
        $closing_time = clone $ghana_time;
        $closing_time->setTime(21, 0, 0);
    } else {
        // If it's after 9 PM, closing is tomorrow at 9 PM
        $closing_time = clone $ghana_time;
        $closing_time->add(new DateInterval('P1D')); // Add 1 day
        $closing_time->setTime(21, 0, 0);
    }
    
    return $closing_time;
}

/**
 * Get formatted time string for display
 * @return string Current Ghana time formatted for display
 */
function get_ghana_time_display() {
    $ghana_time = get_ghana_time();
    return $ghana_time->format('g:i A \o\n l, F j, Y');
}

/**
 * Get time until next status change (opening or closing)
 * @return array Array with 'seconds', 'next_action', 'next_time'
 */
function get_time_until_status_change() {
    $ghana_time = get_ghana_time();
    
    if (is_website_closed()) {
        $next_time = get_next_opening_time();
        $next_action = 'open';
    } else {
        $next_time = get_next_closing_time();
        $next_action = 'close';
    }
    
    $diff = $next_time->getTimestamp() - $ghana_time->getTimestamp();
    
    return [
        'seconds' => $diff,
        'next_action' => $next_action,
        'next_time' => $next_time,
        'formatted_time' => $next_time->format('g:i A')
    ];
}

/**
 * Get JavaScript-compatible timezone offset for Ghana
 * @return int Timezone offset in minutes
 */
function get_ghana_timezone_offset() {
    $ghana_timezone = new DateTimeZone('Africa/Accra');
    $ghana_time = new DateTime('now', $ghana_timezone);
    return $ghana_timezone->getOffset($ghana_time) / 60; // Convert seconds to minutes
}
?>
