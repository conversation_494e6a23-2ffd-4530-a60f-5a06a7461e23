<?php
/**
 * Time Management System for ElsBee Data
 * Handles Ghana timezone time checking for website closure
 * Closed: 9 PM - 7 AM Ghana time (GMT+0)
 * Open: 7 AM - 9 PM Ghana time (GMT+0)
 */

/**
 * Get current time in Ghana timezone
 * @return DateTime Current time in Ghana timezone
 */
function get_ghana_time() {
    // Ghana is GMT+0 (same as UTC)
    $ghana_timezone = new DateTimeZone('Africa/Accra');
    return new DateTime('now', $ghana_timezone);
}

/**
 * Check if the website should be closed
 * @return bool True if website should be closed, false if open
 */
function is_website_closed() {
    $ghana_time = get_ghana_time();
    $current_hour = (int)$ghana_time->format('H'); // 24-hour format
    
    // Closed from 21:00 (9 PM) to 06:59 (6:59 AM)
    // Open from 07:00 (7 AM) to 20:59 (8:59 PM)
    return ($current_hour >= 21 || $current_hour < 7);
}

/**
 * Get the next opening time
 * @return DateTime Next opening time (7 AM Ghana time)
 */
function get_next_opening_time() {
    $ghana_time = get_ghana_time();
    $current_hour = (int)$ghana_time->format('H');
    
    // If it's before 7 AM, opening is today at 7 AM
    if ($current_hour < 7) {
        $opening_time = clone $ghana_time;
        $opening_time->setTime(7, 0, 0);
    } else {
        // If it's after 9 PM, opening is tomorrow at 7 AM
        $opening_time = clone $ghana_time;
        $opening_time->add(new DateInterval('P1D')); // Add 1 day
        $opening_time->setTime(7, 0, 0);
    }
    
    return $opening_time;
}

/**
 * Get the next closing time
 * @return DateTime Next closing time (9 PM Ghana time)
 */
function get_next_closing_time() {
    $ghana_time = get_ghana_time();
    $current_hour = (int)$ghana_time->format('H');
    
    // If it's before 9 PM today, closing is today at 9 PM
    if ($current_hour < 21) {
        $closing_time = clone $ghana_time;
        $closing_time->setTime(21, 0, 0);
    } else {
        // If it's after 9 PM, closing is tomorrow at 9 PM
        $closing_time = clone $ghana_time;
        $closing_time->add(new DateInterval('P1D')); // Add 1 day
        $closing_time->setTime(21, 0, 0);
    }
    
    return $closing_time;
}

/**
 * Get formatted time string for display
 * @return string Current Ghana time formatted for display
 */
function get_ghana_time_display() {
    $ghana_time = get_ghana_time();
    return $ghana_time->format('g:i A \o\n l, F j, Y');
}

/**
 * Get time until next status change (opening or closing)
 * @return array Array with 'seconds', 'next_action', 'next_time'
 */
function get_time_until_status_change() {
    $ghana_time = get_ghana_time();
    
    if (is_website_closed()) {
        $next_time = get_next_opening_time();
        $next_action = 'open';
    } else {
        $next_time = get_next_closing_time();
        $next_action = 'close';
    }
    
    $diff = $next_time->getTimestamp() - $ghana_time->getTimestamp();
    
    return [
        'seconds' => $diff,
        'next_action' => $next_action,
        'next_time' => $next_time,
        'formatted_time' => $next_time->format('g:i A')
    ];
}

/**
 * Get JavaScript-compatible timezone offset for Ghana
 * @return int Timezone offset in minutes
 */
function get_ghana_timezone_offset() {
    $ghana_timezone = new DateTimeZone('Africa/Accra');
    $ghana_time = new DateTime('now', $ghana_timezone);
    return $ghana_timezone->getOffset($ghana_time) / 60; // Convert seconds to minutes
}
?>
