<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// Determine the base path of the application (assuming this file is in a subdirectory)
$base_path = '/datahub/'; // Adjust this if your application is in a different subdirectory

require_login($base_path);

$user = $_SESSION['user'];
$success = $error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        $name = $_POST['name'];
        $email = $_POST['email'];
        $image_path = $user['image'] ?? null; // Get existing image path
        $phone_number = $_POST['phone_number'] ?? null; // Get the phone number, allow null

        // Handle image upload
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $file_tmp_path = $_FILES['profile_image']['tmp_name'];
            $file_name = $_FILES['profile_image']['name'];
            $file_size = $_FILES['profile_image']['size'];
            $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
            $allowed_ext = array('jpg', 'jpeg', 'png', 'gif');

            if (in_array($file_ext, $allowed_ext) && $file_size <= 5000000) { // 5MB max file size
                $new_file_name = uniqid('', true) . '.' . $file_ext;
                $upload_path = '../assets/uploads/profile/' . $new_file_name;
                if (move_uploaded_file($file_tmp_path, $upload_path)) {
                    // Delete old image if it exists and is not the default
                    if ($image_path && $image_path !== 'assets/images/profile.png' && file_exists('../' . $image_path)) {
                        unlink('../' . $image_path);
                    }
                    $image_path = 'assets/uploads/profile/' . $new_file_name; // Store path relative to site root
                } else {
                    $error = 'Error uploading file.';
                }
            } else {
                $error = 'Invalid file type or size. Max 5MB, allowed types: JPG, JPEG, PNG, GIF.';
            }
        }

        if (!$error) {
            $stmt = $conn->prepare('UPDATE users SET full_name = ?, email = ?, image = ?, phone_number = ? WHERE id = ?');
            $stmt->bind_param('ssssi', $name, $email, $image_path, $phone_number, $user['id']);
            if ($stmt->execute()) {
                // Re-fetch user data to update session with latest info
                $stmt_fetch = $conn->prepare('SELECT * FROM users WHERE id = ?');
                $stmt_fetch->bind_param('i', $user['id']);
                $stmt_fetch->execute();
                $result_fetch = $stmt_fetch->get_result();
                $_SESSION['user'] = $result_fetch->fetch_assoc();

                $success = 'Profile updated successfully!';
            } else {
                $error = 'Failed to update profile.';
            }
        }
    }
    if (isset($_POST['change_password'])) {
        $current = $_POST['current_password'];
        $new = $_POST['new_password'];
        $confirm = $_POST['confirm_password'];
        $stmt = $conn->prepare('SELECT password FROM users WHERE id = ?');
        $stmt->bind_param('i', $user['id']);
        $stmt->execute();
        $stmt->bind_result($hash);
        $stmt->fetch();
        $stmt->close();
        if (!password_verify($current, $hash)) {
            $error = 'Current password is incorrect.';
        } elseif ($new !== $confirm) {
            $error = 'New passwords do not match.';
        } else {
            $new_hash = password_hash($new, PASSWORD_DEFAULT);
            $stmt = $conn->prepare('UPDATE users SET password = ? WHERE id = ?');
            $stmt->bind_param('si', $new_hash, $user['id']);
            if ($stmt->execute()) {
                $success = 'Password changed successfully!';
            } else {
                $error = 'Failed to change password.';
            }
        }
    }
}
include '../includes/header.php';
?>
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card p-4 shadow-sm">
        <div class="text-center mb-3">
          <?php
          $profile_image_src = isset($user['image']) && $user['image'] ? $base_path . $user['image'] : $base_path . 'assets/images/profile.png';
          ?>
          <img src="<?php echo $profile_image_src; ?>" class="rounded-circle mb-2" width="100" height="100" alt="Profile Image">
          <h4><?php echo htmlspecialchars($user['full_name']); ?></h4>
          <div class="text-muted"><?php echo htmlspecialchars($user['email']); ?></div>
          <span class="badge mt-2">Account Type: <?php echo htmlspecialchars($user['account_type']); ?></span>
        </div>
        <?php if($success): ?><div class="alert alert-success"><?php echo $success; ?></div><?php endif; ?>
        <?php if($error): ?><div class="alert alert-danger"><?php echo $error; ?></div><?php endif; ?>
        <form method="post" class="mb-4" enctype="multipart/form-data">
          <h5>Update Profile</h5>
          <div class="mb-3">
            <label class="form-label">Full Name</label>
            <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Email</label>
            <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Phone Number</label>
            <input type="tel" name="phone_number" class="form-control" value="<?php echo htmlspecialchars($user['phone_number'] ?? ''); ?>">
          </div>
          <div class="mb-3">
            <label for="profile_image" class="form-label">Profile Image</label>
            <input type="file" name="profile_image" id="profile_image" class="form-control" accept="image/*">
          </div>
          <button type="submit" name="update_profile" class="btn btn-primary w-100">Update Profile</button>
        </form>
        <form method="post">
          <h5>Change Password</h5>
          <div class="mb-3">
            <label>Current Password</label>
            <input type="password" name="current_password" class="form-control" required>
          </div>
          <div class="mb-3">
            <label>New Password</label>
            <input type="password" name="new_password" class="form-control" required>
          </div>
          <div class="mb-3">
            <label>Confirm New Password</label>
            <input type="password" name="confirm_password" class="form-control" required>
          </div>
          <button type="submit" name="change_password" class="btn btn-secondary w-100">Change Password</button>
        </form>
      </div>
    </div>
  </div>
</div>
<?php include '../includes/footer.php'; ?> 