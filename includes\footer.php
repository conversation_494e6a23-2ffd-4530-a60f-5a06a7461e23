</div> <!-- .content -->
<footer class="bg-dark text-white mt-5 pt-4 pb-2">
  <div class="container">
    <div class="row">
      <div class="col-md-4 mb-3">
        <h5>About Us</h5>
        <p>ElsBee Data is your trusted partner for buying data bundles online. We provide seemless data bundles for all major networks in Ghana.</p>
      </div>
      <div class="col-md-4 mb-3">
        <h5>Quick Links</h5>
        <ul class="list-unstyled">
          <li><a href="/store/index.php" class="text-white">Data Bundles</a></li>
          <li><a href="/index.php#how-it-works" class="text-white">How It Works</a></li>
          <li><a href="/index.php#testimonials" class="text-white">Testimonials</a></li>
          <li><a href="/index.php#contact" class="text-white">Contact</a></li>
        </ul>
      </div>
      <div class="col-md-4 mb-3">
        <h5>Follow Us</h5>
        <a href="https://chat.whatsapp.com/CaiJeS1VgKJJBDz1dHWDVT" class="text-white me-2"><i class="bi bi-whatsapp fs-4"></i></a>
      </div>
    </div>
    <div class="text-center mt-3">
      <small>&copy; 2025 ElsBee Data. All rights reserved.</small>
    </div>
  </div>
</footer>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    try {
        const cartModal = document.getElementById('cartModal');
        const cartModalBody = cartModal ? cartModal.querySelector('.modal-body') : null;
        const cartModalTotal = document.getElementById('cart-modal-total');
        const removeConfirmModal = document.getElementById('removeConfirmModal');
        const confirmRemoveBtn = document.getElementById('confirmRemoveBtn');
        
        let itemToRemoveKey = null; // Variable to store the key of the item to be removed

        if (cartModal) {
            try {
                cartModal.addEventListener('show.bs.modal', function () {
                    // Clear previous content and show loading message
                    if (cartModalBody) {
                        cartModalBody.innerHTML = 'Loading cart...';
                    }
                    if (cartModalTotal) {
                         cartModalTotal.innerText = ''; // Clear previous total
                    }

                    // Fetch cart content via AJAX
                    fetchCartContent();
                });
            } catch (e) { console.error('Error adding cart modal show listener:', e); }
        }

         // Function to fetch and display cart content
        function fetchCartContent() {
            try {
                fetch('<?php echo $base_path; ?>cart/get_cart_modal_content.php')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok ' + response.statusText);
                        }
                        return response.text();
                    })
                    .then(html => {
                        if (cartModalBody) {
                            cartModalBody.innerHTML = html;
                        }
                         // Re-calculate and update cart count after fetching content
                         updateCartCountBadge();
                    })
                    .catch(error => {
                        console.error('Error fetching cart content:', error);
                        if (cartModalBody) {
                            cartModalBody.innerHTML = '<div class="alert alert-danger">Failed to load cart content.</div>';
                        }
                        if (cartModalTotal) {
                            cartModalTotal.innerText = ''; // Clear total on error
                        }
                    });
            } catch (e) { console.error('Error in fetchCartContent:', e); }
        }

        // Function to update the cart count badge
        function updateCartCountBadge(count = null) {
            try {
                // This requires fetching the cart data again or getting the count from the server
                // For simplicity, let's fetch the full cart data (headers only) to get the count.
                // A more efficient way would be a dedicated endpoint to get just the count.
                // Only fetch if count is not provided (e.g., on initial page load or modal open)
                if (count === null) {
                     fetch('<?php echo $base_path; ?>cart/get_cart_count.php') // We will create this endpoint
                         .then(response => response.json())
                         .then(data => {
                             if (data.success) {
                                 const cartCountElement = document.querySelector('.cart-count');
                                 if (cartCountElement) {
                                     cartCountElement.innerText = data.count;
                                 }
                             } else {
                                 console.error('Error in fetched cart count data:', data.error);
                             }
                         })
                         .catch(error => {
                             console.error('Error fetching cart count:', error);
                             // Optionally, set count to a default or error state
                         });
                } else {
                    // Use the provided count
                     const cartCountElement = document.querySelector('.cart-count');
                     if (cartCountElement) {
                         cartCountElement.innerText = count;
                     }
                }
            } catch (e) { console.error('Error in updateCartCountBadge:', e); }
        }

        // Event delegation for remove buttons inside the modal
        if (cartModalBody) {
            try {
                cartModalBody.addEventListener('click', function(e) {
                    if (e.target.classList.contains('remove-from-modal') || e.target.closest('.remove-from-modal')) {
                        const button = e.target.closest('.remove-from-modal');
                        itemToRemoveKey = button.dataset.cartKey; // Store the key
                        

                        if (removeConfirmModal) {
                            // Show the custom confirmation modal
                            var removeConfirmModalInstance = new bootstrap.Modal(removeConfirmModal);
                            removeConfirmModalInstance.show();
                        }
                    }
                });
            } catch (e) { console.error('Error adding cart modal body click listener:', e); }
        }

        // Handle click on the 'Remove' button in the confirmation modal
        if (confirmRemoveBtn) {
            try {
                confirmRemoveBtn.addEventListener('click', function() {
                    if (itemToRemoveKey !== null) { // Check against null, not just truthiness
                        // Close the confirmation modal
                        if (removeConfirmModal) {
                            var removeConfirmModalInstance = bootstrap.Modal.getInstance(removeConfirmModal);
                            if (removeConfirmModalInstance) {
                                 removeConfirmModalInstance.hide();
                            }
                        }

                        // Perform AJAX removal
                        const formData = new FormData();
                        formData.append('cart_key', itemToRemoveKey);

                        fetch('<?php echo $base_path; ?>cart/remove_from_cart_modal.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                displayAlert('Item removed from cart.', 'success'); // Use styled alert
                                // Refresh the cart modal content
                                fetchCartContent();
                                // Update cart count badge
                                updateCartCountBadge(data.cart_count);
                            } else {
                                 displayAlert('Error removing item from cart: ' + data.error, 'danger'); // Use styled alert
                            }
                        })
                        .catch(error => {
                            console.error('Remove AJAX error:', error);
                             displayAlert('An unexpected error occurred during removal.', 'danger'); // Use styled alert
                        })
                        .finally(() => {
                             itemToRemoveKey = null; // Reset the stored key
                        });
                    }
                });
            } catch (e) { console.error('Error adding confirm remove button listener:', e); }
        }

        // Initial check and update of cart count when the page loads
        try {
            updateCartCountBadge(<?php echo $cart_item_count; ?>);
        } catch (e) { console.error('Error in initial updateCartCountBadge:', e); }


        // Explicitly initialize Bootstrap components
        try {
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
              return new bootstrap.Dropdown(dropdownToggleEl);
            });

            var collapseElementList = [].slice.call(document.querySelectorAll('.navbar-collapse'));
            var collapseList = collapseElementList.map(function (collapseEl) {
              return new bootstrap.Collapse(collapseEl, { toggle: false });
            });
        } catch (e) { console.error('Error explicitly initializing Bootstrap components:', e); }

    } catch (e) { console.error('Error in DOMContentLoaded listener:', e); }
});
</script>

</body>
</html> 