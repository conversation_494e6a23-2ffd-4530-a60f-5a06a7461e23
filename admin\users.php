<?php
session_start();
require '../includes/db.php';
require '../includes/auth.php';

// require_login() will redirect to login page if not logged in
require_login($base_path);

if ($_SESSION['user']['account_type'] !== 'Admin') {
    header('Location: ' . $base_path . 'index.php');
    exit;
}
$users = $conn->query('SELECT * FROM users ORDER BY created_at DESC');
include '../includes/header.php';
?>
<div class="container py-5">
  <h2 class="mb-4">Manage Users</h2>
  <div class="table-responsive">
    <table class="table align-middle">
      <thead>
        <tr>
          <th>ID</th>
          <th>Name</th>
          <th>Email</th>
          <th>Number</th>
          <th>Account Type</th>2
          <th>Created</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
      <?php while($u = $users->fetch_assoc()): ?>
        <tr>
          <td><?php echo $u['id']; ?></td>
          <td><?php echo htmlspecialchars($u['full_name']); ?></td>
          <td><?php echo htmlspecialchars($u['email']); ?></td>
          <td><?php echo $u['phone_number']; ?></td>
          <td><?php echo htmlspecialchars($u['account_type']); ?></td>
          <td><?php echo date('Y-m-d', strtotime($u['created_at'])); ?></td>
          <td>
            <a href="edit_user.php?id=<?php echo $u['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
            <a href="delete_user.php?id=<?php echo $u['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Delete user?');">Delete</a>
          </td>
        </tr>
      <?php endwhile; ?>
      </tbody>
    </table>
  </div>
  <a href="<?php echo $base_path; ?>admin/dashboard.php" class="btn btn-secondary mt-3">Back to Dashboard</a>
</div>
<?php include '../includes/footer.php'; ?> 