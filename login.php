<?php
session_start();
require 'includes/db.php';

// If user is already logged in, redirect to home or requested page
if (isset($_SESSION['user'])) {
    $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : 'index.php';
    // Validate redirect URL to prevent open redirect vulnerability
    if (strpos($redirect, '://') === false && strpos($redirect, '//') === false) {
        header('Location: ' . $redirect);
    } else {
        header('Location: index.php');
    }
    exit;
}

$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'];
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        $stmt = $conn->prepare('SELECT * FROM users WHERE email = ?');
        $stmt->bind_param('s', $email);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($user = $result->fetch_assoc()) {
            if (password_verify($password, $user['password'])) {
                $_SESSION['user'] = $user;
                // Validate redirect URL to prevent open redirect vulnerability
                $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : 'index.php';
                if (strpos($redirect, '://') === false && strpos($redirect, '//') === false) {
                    header('Location: ' . $redirect);
                } else {
                    header('Location: index.php');
                }
                exit;
            }
        }
        $error = 'Invalid email or password.';
    }
}

// Store the redirect URL in a hidden field if it exists
$redirect_url = isset($_GET['redirect']) ? htmlspecialchars($_GET['redirect']) : '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - ElsBee Data</title>
  <link href="assets/css/style.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="login-register-bg">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-6 col-lg-5">
        <div class="auth-card">
          <h3><i class="bi bi-box-arrow-in-right me-2"></i>Welcome Back</h3>
          <?php if($error): ?>
            <div class="alert alert-danger">
              <i class="bi bi-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
            </div>
          <?php endif; ?>
          <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . ($redirect_url ? '?redirect=' . urlencode($redirect_url) : '')); ?>">
            <?php if($redirect_url): ?>
              <input type="hidden" name="redirect" value="<?php echo htmlspecialchars($redirect_url); ?>">
            <?php endif; ?>
            <div class="form-floating mb-3">
              <input type="email" name="email" class="form-control" id="email" placeholder="<EMAIL>" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
              <label for="email"><i class="bi bi-envelope me-2"></i>Email address</label>
            </div>
            <div class="form-floating mb-4">
              <input type="password" name="password" class="form-control" id="password" placeholder="Password" required>
              <label for="password"><i class="bi bi-lock me-2"></i>Password</label>
            </div>
            <button type="submit" class="btn w-100">
              <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
            </button>
          </form>
          <div class="auth-links">
            <p class="mb-0">Don't have an account? <a href="register.php<?php echo $redirect_url ? '?redirect=' . urlencode($redirect_url) : ''; ?>">Create Account</a></p>
            <p class="mb-0"><a href="forgot_password.php">Forgot Password?</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
